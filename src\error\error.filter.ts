import {
  ArgumentsHost,
  BadRequestException,
  Catch,
  ExceptionFilter,
  HttpStatus,
  Logger,
  NotFoundException,
  UnprocessableEntityException,
} from '@nestjs/common';
import { AxiosError } from 'axios';
import { Response } from 'express';
import { HttpStatusMessage } from 'src/util/http-status-message';
import { BusinessException } from './business-exception';
import { RequiredParameterNotPresentException } from './required-parameter-not-present-exception';

@Catch()
export class ErrorFilter implements ExceptionFilter {
  private logger: Logger;

  constructor() {
    this.logger = new Logger(ErrorFilter.name);
  }

  catch(exception: Error, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();

    if (exception.stack) {
      this.logger.error(
        `${exception.name}: ${exception.message}`,
        exception.stack,
      );
    } else {
      this.logger.error(`${exception.name}: ${exception.message}`);
    }

    if (exception instanceof AxiosError) {
      return this.handleAxiosError(exception, response);
    }

    if (exception instanceof BadRequestException) {
      return this.hadleBadRequest(exception, response);
    }

    if (exception instanceof NotFoundException) {
      return this.hadleNotFound(exception, response);
    }

    if (exception instanceof UnprocessableEntityException) {
      return this.hadleUnprocessableEntityException(exception, response);
    }

    if (exception instanceof RequiredParameterNotPresentException) {
      return this.hadleRequiredParameterNotPresentException(
        exception,
        response,
      );
    }

    if (exception instanceof BusinessException) {
      return this.hadleBusinessException(exception, response);
    }

    return response
      .status(HttpStatus.INTERNAL_SERVER_ERROR)
      .json(JSON.parse('{ "message": "generic error" }'));
  }

  handleAxiosError(exception: AxiosError, response: Response) {
    let status = HttpStatus.SERVICE_UNAVAILABLE;
    let data = {
      message: 'A dependent service is currently unavailable',
      url: exception.config?.url,
      method: exception.config?.method,
      //headers: exception.config?.headers,
      status: status,
    };

    if (exception.response !== undefined) {
      status = exception.response.status;

      // Se o status não for SERVICE_UNAVAILABLE, use os dados da resposta do erro
      if (exception.response.status !== HttpStatus.SERVICE_UNAVAILABLE) {
        data = {
          ...data,
          message: exception?.response?.data.toString(),
          status: exception.response.status,
          url: exception.config?.url,
          method: exception.config?.method,
        };
      }
    }

    return response.status(status).json(data);
  }

  hadleBadRequest(exception: BadRequestException, response: Response) {
    const status = HttpStatus.BAD_REQUEST;
    let message: string = 'Bad Request';

    if (Object.values(exception.getResponse())[1]) {
      message = Object.values(exception.getResponse())[1];
    }

    const data = JSON.parse(`{ "message": ${JSON.stringify(message)} }`);

    return response.status(status).json(data);
  }

  hadleNotFound(exception: NotFoundException, response: Response) {
    const status = HttpStatus.NOT_FOUND;
    let message: string =
      exception.message != 'Not Found'
        ? exception.message
        : HttpStatusMessage.notFound.description;

    const data = JSON.parse(`{ "message": ${JSON.stringify(message)} }`);

    return response.status(status).json(data);
  }

  hadleUnprocessableEntityException(
    exception: UnprocessableEntityException,
    response: Response,
  ) {
    const status = 422;
    let message: string = 'Unprocessable Entity';

    if (Object.values(exception.getResponse())[1]) {
      message = Object.values(exception.getResponse())[1];
    }

    const data = JSON.parse(`{ "message": ${JSON.stringify(message)} }`);

    return response.status(status).json(data);
  }

  hadleRequiredParameterNotPresentException(
    exception: any,
    response: Response,
  ) {
    let status = HttpStatus.BAD_REQUEST;
    let data = JSON.parse(`{ "message": "${exception.message}" }`);

    return response.status(status).json(data);
  }

  hadleBusinessException(exception: any, response: Response) {
    let status = HttpStatus.BAD_REQUEST;
    let data = JSON.parse(`{ "message": "${exception.message}" }`);

    return response.status(status).json(data);
  }
}
