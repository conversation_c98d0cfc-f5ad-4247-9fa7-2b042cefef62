import {
  Injectable,
  Scope,
  UnprocessableEntityException,
} from '@nestjs/common';
import { Context } from '../model/context';
import { RequestCacheManager } from './request-cache-manager';

@Injectable({ scope: Scope.REQUEST })
export class ContextService {
  static CONTEXT = 'CONTEXT';

  public current(): Context {
    const current = RequestCacheManager.get(ContextService.CONTEXT);

    if (!current) {
      throw new UnprocessableEntityException(
        'There was an internal error getting context data',
      );
    }

    return current;
  }

  public set(context: Context): void {
    RequestCacheManager.set(ContextService.CONTEXT, context);
  }

  public clear(): void {
    RequestCacheManager.remove(ContextService.CONTEXT);
  }
}
