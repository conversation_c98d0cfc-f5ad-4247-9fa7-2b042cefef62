apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  name: bff-typescript-express-web-travel-insurance-ingress
  namespace: corp-sva
  annotations:
    kubernetes.io/ingress.class: "nginx-private"
    nginx.org/ssl-backends: "bff-typescript-express-web-travel-insurance-service"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
spec:
  rules:
    - host: bff-typescript-express-web-travel-insurance.__SERVICE_ENV__-cvc.com.br
      http:
        paths:
          - backend:
              serviceName: bff-typescript-express-web-travel-insurance-service
              servicePort: 8080
