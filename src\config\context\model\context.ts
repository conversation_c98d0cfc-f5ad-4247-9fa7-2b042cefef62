import { Credential } from './credential';
import { RequestHeaders } from './request-headers.enum';

export class Context {
  private _userToken: string;
  private _transactionId: string;
  private _credential: Credential;
  private _gtwPricing?: string;

  constructor(
    userToken: string,
    transactionId: string,
    credential: Credential,
    gtwPricing?: string,
  ) {
    this._userToken = userToken;
    this._transactionId = transactionId;
    this._credential = credential;
    this._gtwPricing = gtwPricing;
  }

  public get userToken() {
    return this._userToken;
  }

  public set userToken(userToken) {
    this._userToken = userToken;
  }

  public get transactionId() {
    return this._transactionId;
  }

  public set transactionId(transactionId) {
    this._transactionId = transactionId;
  }

  public get credential() {
    return this._credential;
  }

  public set credential(credential) {
    this._credential = credential;
  }

  public get gtwPricing() {
    return this._gtwPricing;
  }

  public set gtwPricing(gtwPricing: string) {
    this._gtwPricing = gtwPricing;
  }

  public getCommomRequestHeaders(): any {
    const headers: any = {
      [RequestHeaders.SEC_USER_TOKEN]: this._userToken,
      [RequestHeaders.TRANSACTION_ID]: this._transactionId,
    };

    if (this._gtwPricing) {
      headers[RequestHeaders.GTW_PRICING] = this._gtwPricing;
    }

    return headers;
  }

  public getCommomRequestHeadersToCorpSvaInsurance(): any {
    const credential = this._credential;

    const headers: any = {
      [RequestHeaders.AGENT_SIGN]: credential.agentSign,
      [RequestHeaders.BRANCH_ID]: credential.branchId,
      [RequestHeaders.TRANSACTION_ID]: this._transactionId,
      [RequestHeaders.USERNAME]: credential.user,
    };

    if (this._gtwPricing) {
      headers[RequestHeaders.GTW_PRICING] = this._gtwPricing;
    }

    return headers;
  }
}
