environment:
  - name: TI
    farm:
      - name: default
        kubernetes:
          deployment:
            resources:
              requests:
                memory: 256Mi
                cpu: 80m
              limits:
                memory: 800Mi
                cpu: 500m
            readinessProbe:
              health-check:
                path: /health
                port: 8080
              initialDelaySeconds: 30
            livenessProbe:
              health-check:
                path: /health
                port: 8080
              initialDelaySeconds: 30
            ports:
              - containerPort: 8080
              - containerPort: 5005
          hpa:
            minReplicas: 6
            maxReplicas: 12
            metrics:
              resources:
                - name: cpu
                  target:
                    type: Utilization
                    averageUtilization: 500
                - name: memory
                  target:
                    type: Utilization
                    averageUtilization: 250
          service:
            ports:
              - name: mainport
                port: 8080
              - name: secport
                port: 5005
          ingress:
            externalDnsType: nginx-private
            ingressClassName: nginx-private
            type: http
            rules:
              - host: bff-typescript-express-web-travel-insurance
                service:
                  port:
                    number: 8080
          configmap:
            data:
              - name: VAULT_HOST
                value: vault-dev.services.cvc.com.br
              - name: VAULT_SCHEME
                value: http
              - name: CONSUL_HOST
                value: consul-dev.services.cvc.com.br
              - name: CONSUL_PORT
                value: "8500"
              - name: CONSUL_PATH
                value: corp-sva/bff-typescript-express-web-travel-insurance
              - name: NODE_ENV
                value: ti
              - name: ADDITIONAL_OPTS
                value: ' '
              - name: PREFIX
                value: bff-typescript-express-web-travel-insurance
  - name: QA-TMP
    farm:
      - name: default
        kubernetes:
          deployment:
            resources:
              requests:
                memory: 256Mi
                cpu: 80m
              limits:
                memory: 800Mi
                cpu: 500m
            readinessProbe:
              health-check:
                path: /health
                port: 8080
              initialDelaySeconds: 30
            livenessProbe:
              health-check:
                path: /health
                port: 8080
              initialDelaySeconds: 30
            ports:
              - containerPort: 8080
              - containerPort: 5005
          hpa:
            minReplicas: 6
            maxReplicas: 12
            metrics:
              resources:
                - name: cpu
                  target:
                    type: Utilization
                    averageUtilization: 500
                - name: memory
                  target:
                    type: Utilization
                    averageUtilization: 250
          service:
            ports:
              - name: mainport
                port: 8080
              - name: secport
                port: 5005
          ingress:
            externalDnsType: nginx-private
            ingressClassName: nginx-private
            type: http
            rules:
              - host: bff-typescript-express-web-travel-insurance
                service:
                  port:
                    number: 8080
          configmap:
            data:
              - name: NODE_ENV
                value: qa
              - name: ADDITIONAL_OPTS
                value: ' '
              - name: PREFIX
                value: bff-typescript-express-web-travel-insurance
              - name: VAULT_HOST
                value: vault.qa.cvc.intra
              - name: VAULT_SCHEME
                value: http
              - name: CONSUL_HOST
                value: consul.qa.cvc.intra
              - name: CONSUL_PORT
                value: "8500"
              - name: CONSUL_PATH
                value: corp-sva/bff-typescript-express-web-travel-insurance
  - name: QA
    farm:
      - name: default
        kubernetes:
          deployment:
            resources:
              requests:
                memory: 256Mi
                cpu: 80m
              limits:
                memory: 800Mi
                cpu: 500m
            readinessProbe:
              health-check:
                path: /health
                port: 8080
              initialDelaySeconds: 30
            livenessProbe:
              health-check:
                path: /health
                port: 8080
              initialDelaySeconds: 30
            ports:
              - containerPort: 8080
              - containerPort: 5005
          hpa:
            minReplicas: 6
            maxReplicas: 12
            metrics:
              resources:
                - name: cpu
                  target:
                    type: Utilization
                    averageUtilization: 500
                - name: memory
                  target:
                    type: Utilization
                    averageUtilization: 250
          service:
            ports:
              - name: mainport
                port: 8080
              - name: secport
                port: 5005
          ingress:
            externalDnsType: nginx-private
            ingressClassName: nginx-private
            type: http
            rules:
              - host: bff-typescript-express-web-travel-insurance
                service:
                  port:
                    number: 8080
          configmap:
            data:
              - name: NODE_ENV
                value: qa
              - name: ADDITIONAL_OPTS
                value: ' '
              - name: PREFIX
                value: bff-typescript-express-web-travel-insurance
              - name: VAULT_HOST
                value: vault.qa.cvc.intra
              - name: VAULT_SCHEME
                value: http
              - name: CONSUL_HOST
                value: consul.qa.cvc.intra
              - name: CONSUL_PORT
                value: "8500"
              - name: CONSUL_PATH
                value: corp-sva/bff-typescript-express-web-travel-insurance
  - name: PILOT
    farm:
      - name: default
        kubernetes:
          deployment:
            resources:
              requests:
                memory: 256Mi
                cpu: 80m
              limits:
                memory: 800Mi
                cpu: 500m
            readinessProbe:
              health-check:
                path: /health
                port: 8080
              initialDelaySeconds: 30
            livenessProbe:
              health-check:
                path: /health
                port: 8080
              initialDelaySeconds: 30
            ports:
              - containerPort: 8080
              - containerPort: 5005
          hpa:
            minReplicas: 6
            maxReplicas: 12
            metrics:
              resources:
                - name: cpu
                  target:
                    type: Utilization
                    averageUtilization: 500
                - name: memory
                  target:
                    type: Utilization
                    averageUtilization: 250
          service:
            ports:
              - name: mainport
                port: 8080
              - name: secport
                port: 5005
          ingress:
            externalDnsType: nginx-private
            ingressClassName: nginx-private
            type: http
            rules:
              - host: bff-typescript-express-web-travel-insurance
                service:
                  port:
                    number: 8080
          configmap:
            data:
              - name: ADDITIONAL_OPTS
                value: ' '
              - name: PREFIX
                value: bff-typescript-express-web-travel-insurance
              - name: VAULT_HOST
                value: vault.prod.cvc.intra
              - name: VAULT_SCHEME
                value: http
              - name: CONSUL_HOST
                value: consul.prod.cvc.intra
              - name: CONSUL_PORT
                value: "8500"
              - name: CONSUL_PATH
                value: corp-sva/bff-typescript-express-web-travel-insurance
              - name: NODE_ENV
                value: prod
  - name: PROD
    farm:
      - name: default
        kubernetes:
          deployment:
            resources:
              requests:
                memory: 256Mi
                cpu: 80m
              limits:
                memory: 800Mi
                cpu: 500m
            readinessProbe:
              health-check:
                path: /health
                port: 8080
              initialDelaySeconds: 30
            livenessProbe:
              health-check:
                path: /health
                port: 8080
              initialDelaySeconds: 30
            ports:
              - containerPort: 8080
              - containerPort: 5005
          hpa:
            minReplicas: 6
            maxReplicas: 12
            metrics:
              resources:
                - name: cpu
                  target:
                    type: Utilization
                    averageUtilization: 500
                - name: memory
                  target:
                    type: Utilization
                    averageUtilization: 250
          service:
            ports:
              - name: mainport
                port: 8080
              - name: secport
                port: 5005
          ingress:
            externalDnsType: nginx-private
            ingressClassName: nginx-private
            type: http
            rules:
              - host: bff-typescript-express-web-travel-insurance
                service:
                  port:
                    number: 8080
          configmap:
            data:
              - name: ADDITIONAL_OPTS
                value: ' '
              - name: PREFIX
                value: bff-typescript-express-web-travel-insurance
              - name: VAULT_HOST
                value: vault.prod.cvc.intra
              - name: VAULT_SCHEME
                value: http
              - name: CONSUL_HOST
                value: consul.prod.cvc.intra
              - name: CONSUL_PORT
                value: "8500"
              - name: CONSUL_PATH
                value: corp-sva/bff-typescript-express-web-travel-insurance
              - name: NODE_ENV
                value: prod
