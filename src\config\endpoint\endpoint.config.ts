import { Injectable } from '@nestjs/common';

@Injectable()
export class EndpointConfig {
  private corpSvaInsuranceUrl: string;
  private opportunitiesUrl: string;
  private travelVoucherUrl: string;
  private orchestratorOperationsUrl: string;

  constructor() {
    this.opportunitiesUrl = process.env.OPPORTUNITIES_API_URL;
    this.corpSvaInsuranceUrl = process.env.CORP_SVA_INSURANCE_API_URL;
    this.travelVoucherUrl = process.env.TRAVEL_VOUCHER_API_URL;
    this.orchestratorOperationsUrl =
      process.env.ORCHESTRATOR_OPERATIONS_API_URL;
  }

  getCorpSvaInsuranceUrl(): string {
    return this.corpSvaInsuranceUrl;
  }

  getOpportunitiesUrl(): string {
    return this.opportunitiesUrl;
  }

  getTravelVoucherUrl(): string {
    return this.travelVoucherUrl;
  }

  getOrchestratorOperationsUrl(): string {
    return this.orchestratorOperationsUrl;
  }
}
