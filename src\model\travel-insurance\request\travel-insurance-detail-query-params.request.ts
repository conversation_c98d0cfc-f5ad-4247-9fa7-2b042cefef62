import { ApiProperty } from '@nestjs/swagger';
import { IsDateString, IsNotEmpty, IsNumberString } from 'class-validator';

export class TravelInsuranceDetailQueryParamsDTO {
  @IsDateString(
    {},
    {
      message:
        'startDate must be a valid ISO 8601 date string following the pattern yyyy-MM-dd',
    },
  )
  @ApiProperty({
    type: Date,
    format: 'yyyy-MM-dd',
  })
  startDate: string;

  @IsDateString(
    {},
    {
      message:
        'endDate must be a valid ISO 8601 date string following the pattern yyyy-MM-dd',
    },
  )
  @ApiProperty({
    type: Date,
    format: 'yyyy-MM-dd',
  })
  endDate: string;

  @IsNumberString({}, { message: 'zoneId must be a number' })
  @ApiProperty()
  zoneId: number;

  @IsNotEmpty()
  @ApiProperty()
  paxs: string;
}
