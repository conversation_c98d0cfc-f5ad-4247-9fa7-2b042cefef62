import { TerminusModule } from '@nestjs/terminus';
import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { configProviders } from './config/config.providers';
import { controllerProviders } from './controller/controller.providers';
import { bridgeProviders } from './bridge/bridge.providers';
import { serviceProviders } from './service/service.providers';

const ENV = process.env.NODE_ENV;

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ENV !== 'local' ? '.env' : '.env.local',
    }),
    TerminusModule,
    HttpModule,
    JwtModule,
  ],
  controllers: controllerProviders,
  providers: [...configProviders, ...bridgeProviders, ...serviceProviders],
})
export class AppModule {}
