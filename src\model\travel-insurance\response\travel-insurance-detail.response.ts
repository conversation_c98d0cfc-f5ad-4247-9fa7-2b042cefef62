import { TravelInsurance } from '../travel-insurance';
import { ApiProperty } from '@nestjs/swagger';
import { MetaResponse } from './meta.response';
import { MetaAvailResponse } from './meta.avail.response';

export class TravelInsuranceAvailDetailResponse {
  @ApiProperty({ type: TravelInsurance, isArray: false })
  insurance: TravelInsurance;
  @ApiProperty({ type: MetaResponse })
  meta: MetaAvailResponse;
}
