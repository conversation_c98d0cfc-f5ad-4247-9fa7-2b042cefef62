import { TravelInsurance } from 'src/model/travel-insurance/travel-insurance';
import { OpportunitiesItemRequest } from './opportunities-item.request';
import { OpportunitiesParamsRequest } from './opportunities-params.request';

export class OpportunitiesRequest {
  private _params: OpportunitiesParamsRequest;
  private _selectedItems: OpportunitiesItemRequest[];
  private _availableItems: OpportunitiesItemRequest[];

  constructor(
    params: OpportunitiesParamsRequest,
    selectedItems: OpportunitiesItemRequest[],
    availableItems: OpportunitiesItemRequest[],
  ) {
    this._params = params;
    this._selectedItems = selectedItems;
    this._availableItems = availableItems;
  }

  public get params() {
    return this._params;
  }

  public set params(params) {
    this._params = params;
  }

  public get selectedItems() {
    return this._selectedItems;
  }

  public set selectedItems(selectedItems) {
    this._selectedItems = selectedItems;
  }

  public get availableItems() {
    return this._availableItems;
  }

  public set availableItems(availableItems) {
    this._availableItems = availableItems;
  }

  static fromTravelInsurances(
    travelInsurances: TravelInsurance[],
    selectedItems: OpportunitiesItemRequest[],
  ): OpportunitiesRequest {
    const availableItems = travelInsurances.map((travelInsurance) => {
      const itemRequest = new OpportunitiesItemRequest();
      itemRequest.rph = travelInsurance.id;
      itemRequest.rateToken = travelInsurance.insuranceToken;
      return itemRequest;
    });

    const params = OpportunitiesParamsRequest.newInstanceWithDefaultParams();

    return new OpportunitiesRequest(params, selectedItems, availableItems);
  }

  public getBodyToRequest(): any {
    return {
      params: this._params,
      selectedItems: this._selectedItems,
      availableItems: this.availableItems,
    };
  }
}
