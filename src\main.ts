import { ValidationPipe } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import consulConfig from './config/consul/consul.config';

async function bootstrap() {
  await consulConfig();

  const app = await NestFactory.create(AppModule);
  const port = process.env.PORT || 3000;

  app.useGlobalPipes(new ValidationPipe());
  app.enableCors();
  const configSwagger = new DocumentBuilder()
    .setTitle('bff-typescript-express-web-travel-insurance')
    .setDescription('CVC | Experiência e SVAs | SVAs | Seguro Viagem Web')
    .setVersion('1.0.0')
    .build();
  const document = SwaggerModule.createDocument(app, configSwagger);
  SwaggerModule.setup('swagger-ui', app, document);

  console.log(`Server running on port ${port}`);

  await app.listen(port);
}

bootstrap();
