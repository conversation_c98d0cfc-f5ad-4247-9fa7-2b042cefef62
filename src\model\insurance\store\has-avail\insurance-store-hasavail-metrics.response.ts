import { ApiProperty } from '@nestjs/swagger';
import { InsuranceStoreResultResponse } from './insurance-store-hasavail-result.response';
import { InsuranceStorePromotionResponse } from './insurance-store-hasavail-promotion.response';

export class InsuranceStoreMetricsResponse {
  @ApiProperty()
  result: InsuranceStoreResultResponse = new InsuranceStoreResultResponse();

  @ApiProperty({
    required: false,
    type: InsuranceStorePromotionResponse,
    isArray: true,
  })
  promotions: InsuranceStorePromotionResponse[];
}
