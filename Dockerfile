# Build Layer
FROM node:18 AS build
WORKDIR /app
COPY ./ ./

RUN apt-get update && apt-get install -y zip
RUN npm install
RUN npm run build
RUN zip bff-typescript-express-web-travel-insurance ./ -r -x /node_modules/*

# Veracode layer
FROM veracode/api-wrapper-java:latest AS veracode
ARG VERACODE_APP_ID
ARG VERACODE_API_KEY
ARG BUILD_ID
COPY --from=build /app /home/<USER>/app
RUN java -jar /opt/veracode/api-wrapper.jar \
    -vid $VERACODE_APP_ID \
    -vkey $VERACODE_API_KEY \
    -version $BUILD_ID \
    -action UploadAndScan \
    -createprofile true \
    -appname "bff-typescript-express-web-travel-insurance" \
    -filepath app/bff-typescript-express-web-travel-insurance.zip; exit 0;

# Runner Layer
FROM node:18
WORKDIR /app
COPY --from=veracode /home/<USER>/app ./

RUN rm bff-typescript-express-web-travel-insurance.zip

ENV PORT 8080

EXPOSE 8080

ENTRYPOINT ["npm", "start"]
