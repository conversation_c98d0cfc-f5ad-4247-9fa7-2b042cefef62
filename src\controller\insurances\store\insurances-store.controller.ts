import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Query,
  UseFilters,
  UseInterceptors,
} from '@nestjs/common';
import { ApiHeader, ApiOperation, ApiParam, ApiTags } from '@nestjs/swagger';
import { ErrorFilter } from '../../../error/error.filter';
import { ContextInterceptor } from '../../../config/context/interceptor/context-interceptor';
import { RequestHeaders } from '../../../config/context/model/request-headers.enum';
import { AvailParams } from 'src/model/avail/request/avail-params.request';
import { InsuranceStoreService } from 'src/service/insurances/store/insurances-store.service';
import { InsuranceStoreWrapperResponse } from 'src/model/insurance/store/has-avail/insurance-store-hasavail-wrapper.response';
import { InsuranceRequestBody } from '../../../model/opportunities/request/insurance-request-body';
import { InsurancesWebService } from '../../../service/insurances-web/insurances-web.service';

@Controller('insurances/store')
@ApiTags('Insurances Store')
@UseFilters(new ErrorFilter())
@UseInterceptors(ContextInterceptor)
@ApiHeader({ name: RequestHeaders.SEC_USER_TOKEN, required: true })
@ApiHeader({ name: RequestHeaders.TRANSACTION_ID, required: true })
export class InsurancesStoreController {
  constructor(
    private service: InsuranceStoreService,
    private readonly insuranceWebServie: InsurancesWebService,
  ) {}

  @Get('/hasAvail/:rateToken')
  @ApiParam({
    name: 'rateToken',
    description: 'Avail RateToken Identifier',
    type: 'string',
  })
  async hasAvail(
    @Param('rateToken') rateToken: string,
  ): Promise<InsuranceStoreWrapperResponse> {
    const availParams = new AvailParams();
    availParams.pathParam = rateToken;
    return this.service.hasAvail(availParams);
  }

  @Post('/avail')
  @ApiOperation({
    description: 'Hasavail -> check travel insurance availability',
  })
  @HttpCode(HttpStatus.OK)
  async getStoreAvail(
    @Query() query: any,
    @Body() requestBody: InsuranceRequestBody,
  ) {
    return this.insuranceWebServie.getStoreAvails(query, requestBody);
  }

  @Get('/detail/:id')
  @ApiOperation({ description: 'detail -> get travel insurance detail' })
  @HttpCode(HttpStatus.OK)
  async getStoreDetail(@Param('id') id: any, @Query() query: any) {
    return this.insuranceWebServie.getStoreDetails(id, query);
  }
}
