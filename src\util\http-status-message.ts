export class HttpStatusMessage {
  public static readonly ok = new HttpStatusMessage(200, 'Successful request');
  public static readonly created = new HttpStatusMessage(
    201,
    'The request was successful and a new resource was created',
  );
  public static readonly accepted = new HttpStatusMessage(
    202,
    'The request has been successfully received and is being processed',
  );
  public static readonly noContent = new HttpStatusMessage(
    204,
    'The request was successful and there is no content to view',
  );
  public static readonly badRequest = new HttpStatusMessage(
    400,
    'The request does not meet all the necessary conditions',
  );
  public static readonly unauthorized = new HttpStatusMessage(
    401,
    'Not authorized to view',
  );
  public static readonly forbidden = new HttpStatusMessage(
    403,
    'Accessing the resource you were trying to access is forbidden',
  );
  public static readonly notFound = new HttpStatusMessage(
    404,
    'The resouce you are trying to access is not found',
  );

  private constructor(
    public readonly status: number,
    public readonly description: string,
  ) {}
}
