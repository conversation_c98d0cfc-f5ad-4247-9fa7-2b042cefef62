import { ApiProperty } from '@nestjs/swagger';
import { Promotion } from 'src/model/opportunities/promotion/promotion';
import { Benefit } from '../benefit';
import { Rate } from '../rate';
import { TravelInsurance } from '../travel-insurance';
import { InsuranceCommissionResponse } from './commission/insurance-commission.response';
import { MetaForHasavailResponse } from './meta-for-hasavail.response';

export class TravelInsuranceHasavailResponse {
  @ApiProperty()
  id: number;

  @ApiProperty()
  key: string;

  @ApiProperty()
  insuranceToken: string;

  @ApiProperty({ type: Benefit, isArray: true })
  benefits: Benefit[];

  @ApiProperty()
  startDate: string;

  @ApiProperty()
  endDate: string;

  @ApiProperty()
  passengers: number;

  @ApiProperty()
  name: string;

  @ApiProperty()
  description: string;

  @ApiProperty()
  additionalDescription: string;

  @ApiProperty()
  company: string;

  @ApiProperty()
  rate: Rate;

  @ApiProperty()
  taxes: string[];

  @ApiProperty()
  international: boolean;

  @ApiProperty()
  cancelable: boolean;

  @ApiProperty()
  promotion: Promotion;

  @ApiProperty({ type: InsuranceCommissionResponse })
  commission: InsuranceCommissionResponse;

  @ApiProperty({ type: MetaForHasavailResponse })
  meta: MetaForHasavailResponse;

  @ApiProperty()
  acceptableDocuments: string[];

  public static fromTravelInsuranceWithMeta(
    insurance: TravelInsurance,
    meta: MetaForHasavailResponse,
  ): TravelInsuranceHasavailResponse {
    const hasavail = new TravelInsuranceHasavailResponse();

    hasavail.id = insurance.id;
    hasavail.key = insurance.key;
    hasavail.insuranceToken = insurance.insuranceToken;
    hasavail.benefits = insurance.benefits;
    hasavail.startDate = insurance.startDate;
    hasavail.endDate = insurance.endDate;
    hasavail.passengers = insurance.passengers;
    hasavail.name = insurance.name;
    hasavail.description = insurance.description;
    hasavail.additionalDescription = insurance.additionalDescription;
    hasavail.company = insurance.company;
    hasavail.rate = insurance.rate;
    hasavail.taxes = insurance.taxes;
    hasavail.international = insurance.international;
    hasavail.cancelable = insurance.cancelable;
    hasavail.promotion = insurance.promotion;
    hasavail.commission = insurance.commission;
    hasavail.meta = meta;
    hasavail.acceptableDocuments = insurance.acceptableDocuments;

    return hasavail;
  }
}
