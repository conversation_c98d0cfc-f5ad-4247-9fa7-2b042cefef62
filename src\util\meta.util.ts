import { MetricResultResponse } from 'src/model/travel-insurance/response/metric/metric-result.response';
import { MetricResponse } from 'src/model/travel-insurance/response/metric/metric.response';

export class MetaUtil {
  public static applyMetrics(
    metricResult: MetricResultResponse,
    metricToPromotion: MetricResultResponse,
  ): MetricResponse {
    const metricResponse = new MetricResponse();
    metricResponse.result = metricResult;
    metricResponse.promotions = metricToPromotion ? [metricToPromotion] : [];

    return metricResponse;
  }
}
