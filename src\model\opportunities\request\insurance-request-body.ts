import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray, ValidateNested } from 'class-validator';
import { OpportunitiesItemRequest } from './opportunities-item.request';

export class InsuranceRequestBody {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => OpportunitiesItemRequest)
  @ApiProperty({ type: OpportunitiesItemRequest, isArray: true })
  selectedItems: OpportunitiesItemRequest[];
}
