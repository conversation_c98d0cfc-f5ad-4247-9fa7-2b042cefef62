import { Injectable } from '@nestjs/common';

@Injectable()
export class LinksConfig {
  private defaultPartnerUrl: string;
  private defaultImageUrl: string;

  constructor() {
    this.defaultPartnerUrl = process.env.UNIVERSAL_ASSISTANCE_URL;
    this.defaultImageUrl = process.env.UNIVERSAL_ASSISTANCE_LOGO_URL;
  }

  getUniversalAssistanceUrl(): string {
    return this.defaultPartnerUrl;
  }

  getUniversalAssistanceLogoUrl(): string {
    return this.defaultImageUrl;
  }
}
