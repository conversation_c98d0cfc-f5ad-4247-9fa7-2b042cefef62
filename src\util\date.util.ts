export class DateUtil {
  public static plusDays(date: Date, days: number): Date {
    date.setDate(date.getDate() + days);
    return date;
  }

  public static formatToPtBr(date: Date): string {
    let dateISO = date.toISOString();
    dateISO = dateISO.substring(0, dateISO.indexOf('T'));

    const dateChunk = dateISO.split('-');

    return `${dateChunk[2]}/${dateChunk[1]}/${dateChunk[0]}`;
  }
}
