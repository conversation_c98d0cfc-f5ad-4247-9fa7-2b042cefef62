sonar.projectKey=bff-typescript-express-web-travel-insurance
sonar.projectName=bff-typescript-express-web-travel-insurance
sonar.exclusions=**node_modules/**,**dist/**,**/*kubernetes/**,**/app.module.ts,**/main.ts,**/*config/**,**/*config/**,**/*error/**,**/*model/**,**/*test/**,**/*util/http-status-message.ts,**/*service/service.providers.ts
sonar.coverage.exclusions=**/controller/**/*.*,**/bridge/**/*.*
sonar.javascript.lcov.reportPaths=./coverage/lcov.info
