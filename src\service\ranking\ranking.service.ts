import { Injectable } from '@nestjs/common';
import { RankingConfig } from 'src/config/ranking/ranking.config';
import { Benefit } from 'src/model/travel-insurance/benefit';
import { TravelInsurance } from 'src/model/travel-insurance/travel-insurance';
import { StringUtil } from 'src/util/string.util';

@Injectable()
export class RankingService {
  constructor(private readonly config: RankingConfig) {}

  sortBenefits(travelInsurance: TravelInsurance): TravelInsurance {
    if (travelInsurance && travelInsurance.benefits) {
      const benefits = travelInsurance.benefits.map((benefit) =>
        this.applyRankingAndBenefitValue(benefit),
      );

      travelInsurance.benefits = benefits;
    }

    return travelInsurance;
  }

  private applyRankingAndBenefitValue(benefit: Benefit) {
    const ranking = this.findRanking(benefit.id);

    benefit.ranking = ranking ? ranking : this.config.getDefaultRanking();

    if (benefit.benefitValue == 0) {
      benefit.benefitValue = StringUtil.toNumber(benefit.value);
    }

    return benefit;
  }

  private findRanking(key: number): number {
    return this.config.getCustomRanking().get(key);
  }
}
