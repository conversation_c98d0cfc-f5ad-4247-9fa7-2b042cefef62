apiVersion: autoscaling/v2beta2
kind: HorizontalPodAutoscaler
metadata:
  name: bff-typescript-express-web-travel-insurance-hpa
  namespace: corp-sva
  labels:
    app: bff-typescript-express-web-travel-insurance
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: bff-typescript-express-web-travel-insurance-deploy
  minReplicas: 6
  maxReplicas: 12
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 500
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 250
