import { HttpService } from '@nestjs/axios';
import { Injectable, Logger } from '@nestjs/common';
import { Context } from 'src/config/context/model/context';
import { ContextService } from 'src/config/context/service/context.service';
import { EndpointConfig } from 'src/config/endpoint/endpoint.config';
import { TravelInsuranceAvailQueryParamsDTO } from 'src/model/travel-insurance/request/travel-insurance-avail-query-params.request';
import { TravelInsurance } from 'src/model/travel-insurance/travel-insurance';

@Injectable()
export class InsuranceApiBridge {
  private path: string;
  private readonly logger: Logger;

  constructor(
    private readonly endpointConfig: EndpointConfig,
    private readonly httpService: HttpService,
    private readonly contextService: ContextService,
  ) {
    this.path = 'corp-sva-insurance/v3';
    this.logger = new Logger(InsuranceApiBridge.name);
  }

  async avail(
    params: TravelInsuranceAvailQueryParamsDTO,
  ): Promise<TravelInsurance[]> {
    const context = this.getContext();
    const uri = `${this.endpointConfig.getCorpSvaInsuranceUrl()}/${
      this.path
    }/avail`;

    this.logger.log(`Executing http get request at ${uri}`);

    // Log the curl command for this request
    this.printCurlCommand(
      uri,
      context.getCommomRequestHeadersToCorpSvaInsurance(),
      params.toGetAvail(),
    );

    const { data } = await this.httpService.axiosRef.get<TravelInsurance[]>(
      uri,
      {
        headers: context.getCommomRequestHeadersToCorpSvaInsurance(),
        params: params.toGetAvail(),
      },
    );

    return data;
  }

  async hasavail(rateToken: string): Promise<TravelInsurance> {
    const context = this.getContext();
    const uri = `${this.endpointConfig.getCorpSvaInsuranceUrl()}/${
      this.path
    }/hasAvail/${rateToken}`;

    this.logger.log(`Executing http get request at ${uri}`);

    // Log the curl command for this request
    this.printCurlCommand(
      uri,
      context.getCommomRequestHeadersToCorpSvaInsurance(),
    );

    const { data } = await this.httpService.axiosRef.get<TravelInsurance>(uri, {
      headers: context.getCommomRequestHeadersToCorpSvaInsurance(),
    });

    return data;
  }

  public getContext(): Context {
    return this.contextService.current();
  }

  // Método genérico para montar e printar o comando curl sem o --data em GET
  private printCurlCommand(url: string, headers: any, params?: any) {
    try {
      // Adicionar parâmetros de query à URL se existirem
      if (params) {
        const paramString = new URLSearchParams(params).toString();
        url += `?${paramString}`;
      }

      // Iniciar o comando curl com a URL
      let curl = `curl -X GET '${url}'`;

      // Adicionar headers ao comando curl
      Object.keys(headers).forEach((key) => {
        curl += ` -H '${key}: ${headers[key]}'`;
      });

      // Log formatado com uma aparência mais elegante e sem body para GET
      this.logger.log(`
================== CURL GATEWAY ==================
${curl}
=============================
`);
    } catch (error) {
      // Caso ocorra um erro, logamos o erro, mas não interrompemos a execução
      this.logger.log("Error generating CURL command", error);
    }
  }
}
