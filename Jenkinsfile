@Library('cvc-jen<PERSON>-lib')

final def projectConfig = this.readJSON(text: """
{
    "name": "bff-typescript-express-web-travel-insurance",
    "git": {
        "repositoryUrl": "******************:experiencia-e-svas/svas/bff-typescript-express-web-travel-insurance.git"
    },
    "technology": {
        "name": "nodejs",
        "version": "16.15.1",
        "buildCommands": {
            "installDependences": "npm install",
            "buildApp": "npm run build",
        }
    },
    "docker": {
        "dockerfilePath": "Dockerfile"
    },
    "kubernetes": {
        "namespace": "corp-sva"
    }
}
""")

final commons = cvcCorpPipeline.getCommons(this, projectConfig)

pipeline {
    agent any
    stages {
        stage('Deploy TI') {
            steps {
                script {
                    deploy(this, 'TI', projectConfig, commons) {

                    }
                }
            }
        }
        stage('Deploy QA') {
            steps {
                script {
                    deploy(this, 'QA', projectConfig, commons) {

                    }
                }
            }
        }
        stage('Deploy PROD') {
            steps {
                script {
                    deploy(this, 'PROD', projectConfig, commons) {

                    }
                }
            }
        }
    }
    post {
        always {
            dir("${this.env.WORKSPACE}") {
                //Clean Workspace
                deleteDir()
            }
        }
    }
}

