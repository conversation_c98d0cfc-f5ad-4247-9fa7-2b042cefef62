export class StringUtil {
  public static toNumber(value: string): number {
    let number = this.extractNumber(value);
    const containsDecimalPlace = this.containsDecimalPlace(number);

    number = this.assembleNumber(
      number.replace(/,/g, '.'),
      containsDecimalPlace,
    );

    return this.getNumberWithDecimals(number);
  }

  private static containsDecimalPlace(value: string) {
    return value.includes('.') && value.includes(',');
  }

  private static extractNumber(value: string): string {
    return value.replace(/[^0-9,.]/g, '');
  }

  private static assembleNumber(
    value: string,
    containsDecimalPlace: boolean,
  ): string {
    if (value.length == 0 || value === '.') {
      return '0';
    }

    if (!value.includes('.')) {
      return value;
    }

    const chunks = value.split('.');
    const lastChunk = chunks.length - 1;
    const valueContainsDecimalPlace = containsDecimalPlace
      ? containsDecimalPlace
      : chunks[lastChunk].length <= 2;
    const containsOnlyDecimal =
      chunks.length == 2 && chunks[lastChunk - 1].length == 0;
    let result = '';

    for (let chunk = 0; chunk <= lastChunk; chunk++) {
      if (chunk == lastChunk) {
        if (valueContainsDecimalPlace || containsOnlyDecimal) {
          result += '.';
        }
      }

      result += chunks[chunk];
    }

    if (result === '.') {
      result = '0';
    }

    return result;
  }

  private static getNumberWithDecimals(number: string): number {
    return Math.round(Number(number) * 100 + Number.EPSILON) / 100;
  }
}
