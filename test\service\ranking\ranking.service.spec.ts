import { Test, TestingModule } from '@nestjs/testing';
import { RankingConfig } from 'src/config/ranking/ranking.config';
import { RankingService } from 'src/service/ranking/ranking.service';
import { StringUtil } from 'src/util/string.util';
import { TravelInsuranceMock } from '../../../test/mock/travel-insurance.mock';

describe('RankingService', () => {
    let service: RankingService;    
    let config: RankingConfig;        

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [
                RankingService,                                                
                RankingConfig,                            
            ]                                                  
        }).compile();

        service = await module.resolve(RankingService);               
        config = await module.resolve(RankingConfig);             
    });

    it('should be defined', () => {           
        expect(service).toBeDefined();        
        expect(config).toBeDefined();
    });

    it('must sort benefits from travel insurance', () => {

        const travelInsurance = TravelInsuranceMock.getTravelInsurances()[0];

        const expectedRankingOne = travelInsurance.benefits[2].id;
        const expectedRankingTwo = travelInsurance.benefits[1].id;
        const expectedRankingThree = travelInsurance.benefits[3].id;
        const expectedRankingFour = travelInsurance.benefits[0].id;

        const customRanking: Map<number, number> = new Map(); 
        customRanking.set(expectedRankingOne, 1);
        customRanking.set(expectedRankingTwo, 2);
        customRanking.set(expectedRankingThree, 3);

        const defaultRanking = 100;
        
        const configCalledToGetCustomRanking = jest.spyOn(config, 'getCustomRanking')
            .mockImplementation(() => customRanking);

        const configCalledToGetDefaultRanking = jest.spyOn(config, 'getDefaultRanking')
            .mockImplementation(() => defaultRanking);

        service.sortBenefits(travelInsurance);
                    
        expect(travelInsurance).toBeTruthy();  
        expect(travelInsurance.benefits[0].id).toEqual(expectedRankingOne);
        expect(travelInsurance.benefits[1].id).toEqual(expectedRankingTwo);
        expect(travelInsurance.benefits[2].id).toEqual(expectedRankingThree);
        expect(travelInsurance.benefits[3].id).toEqual(expectedRankingFour);
        expect(travelInsurance.benefits[0].ranking).toEqual(1);
        expect(travelInsurance.benefits[1].ranking).toEqual(2);
        expect(travelInsurance.benefits[2].ranking).toEqual(3);
        expect(travelInsurance.benefits[3].ranking).toEqual(defaultRanking);        
        expect(configCalledToGetCustomRanking).toHaveBeenCalledTimes(4);
        expect(configCalledToGetDefaultRanking).toHaveBeenCalledTimes(1);
    });

    it('must sort benefits and apply benefit value when it is zero from travel insurance benefit', () => {

        const travelInsurance = TravelInsuranceMock.getTravelInsurances()[0];

        const expectedRankingOne = travelInsurance.benefits[2].id;
        const expectedRankingTwo = travelInsurance.benefits[1].id;
        const expectedRankingThree = travelInsurance.benefits[3].id;
        const expectedRankingFour = travelInsurance.benefits[0].id;
        const expectedBenefitOneBenefitValue = StringUtil.toNumber(travelInsurance.benefits[1].value);

        const customRanking: Map<number, number> = new Map(); 
        customRanking.set(expectedRankingOne, 1);
        customRanking.set(expectedRankingTwo, 2);
        customRanking.set(expectedRankingThree, 3);

        const defaultRanking = 100;
        
        const configCalledToGetCustomRanking = jest.spyOn(config, 'getCustomRanking')
            .mockImplementation(() => customRanking);

        const configCalledToGetDefaultRanking = jest.spyOn(config, 'getDefaultRanking')
            .mockImplementation(() => defaultRanking);

        service.sortBenefits(travelInsurance);
                    
        expect(travelInsurance).toBeTruthy();  
        expect(travelInsurance.benefits[1].benefitValue).toEqual(expectedBenefitOneBenefitValue)
        expect(travelInsurance.benefits[0].id).toEqual(expectedRankingOne);
        expect(travelInsurance.benefits[1].id).toEqual(expectedRankingTwo);
        expect(travelInsurance.benefits[2].id).toEqual(expectedRankingThree);
        expect(travelInsurance.benefits[3].id).toEqual(expectedRankingFour);
        expect(travelInsurance.benefits[0].ranking).toEqual(1);
        expect(travelInsurance.benefits[1].ranking).toEqual(2);
        expect(travelInsurance.benefits[2].ranking).toEqual(3);
        expect(travelInsurance.benefits[3].ranking).toEqual(defaultRanking);        
        expect(configCalledToGetCustomRanking).toHaveBeenCalledTimes(4);
        expect(configCalledToGetDefaultRanking).toHaveBeenCalledTimes(1);
    });

    it('must sort by default ranking when no custom ranking', () => {        

        const travelInsurance = TravelInsuranceMock.getTravelInsurances()[0];

        const expectedRankingOne = travelInsurance.benefits[0].id;
        const expectedRankingTwo = travelInsurance.benefits[1].id;
        const expectedRankingThree = travelInsurance.benefits[2].id;
        const expectedRankingFour = travelInsurance.benefits[3].id;

        const customRanking: Map<number, number> = new Map();      
        const defaultRanking = 100;  
        
        const configCalledToGetCustomRanking = jest.spyOn(config, 'getCustomRanking')
            .mockImplementation(() => customRanking);

        const configCalledToGetDefaultRanking = jest.spyOn(config, 'getDefaultRanking')
            .mockImplementation(() => defaultRanking);

        service.sortBenefits(travelInsurance);
                    
        expect(travelInsurance).toBeTruthy();  
        expect(travelInsurance.benefits[0].id).toEqual(expectedRankingOne);
        expect(travelInsurance.benefits[1].id).toEqual(expectedRankingTwo);
        expect(travelInsurance.benefits[2].id).toEqual(expectedRankingThree);
        expect(travelInsurance.benefits[3].id).toEqual(expectedRankingFour);
        expect(travelInsurance.benefits[0].ranking).toEqual(defaultRanking);
        expect(travelInsurance.benefits[1].ranking).toEqual(defaultRanking);
        expect(travelInsurance.benefits[2].ranking).toEqual(defaultRanking);
        expect(travelInsurance.benefits[3].ranking).toEqual(defaultRanking);        
        expect(configCalledToGetCustomRanking).toHaveBeenCalledTimes(4);
        expect(configCalledToGetDefaultRanking).toHaveBeenCalledTimes(4);
    });
    
});