import { InsuranceRequestBody } from "src/model/opportunities/request/insurance-request-body";
import { OpportunitiesMock } from "./opportunities.mock";

export class InsuranceRequestBodyMock {

    private constructor() {}

    static getInsuranceRequestBody(): InsuranceRequestBody {

        const requestBody = new InsuranceRequestBody();
        requestBody.selectedItems = OpportunitiesMock.getOpportunitiesItemRequest();

        return requestBody;
    }

}