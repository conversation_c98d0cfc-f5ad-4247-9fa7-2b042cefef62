import { ApiProperty } from '@nestjs/swagger';
import { TravelInsurance } from '../travel-insurance';
import { MetaResponse } from './meta.response';
import { Insurance } from './Insurance';
import { MetaAvailResponse } from './meta.avail.response';

export class AvailResponse {
  @ApiProperty({ type: TravelInsurance, isArray: true })
  insurances: Insurance[];

  @ApiProperty({ type: MetaResponse })
  meta: MetaAvailResponse;
}
