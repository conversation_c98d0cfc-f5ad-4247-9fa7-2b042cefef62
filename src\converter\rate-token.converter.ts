import { Credential } from 'src/config/context/model/credential';
import { TravelInsuranceRateToken } from 'src/model/rate-token/travel-insurance-rate-token';
import { TravelInsuranceAvailQueryParamsDTO } from '../model/travel-insurance/request/travel-insurance-avail-query-params.request';
import { TravelInsurance } from '../model/travel-insurance/travel-insurance';

class Property<T> {
  name: string;
  value?: T;
}

export class RateTokenConverter {
  protected agentSign: Property<string> = { name: 'ags' };
  protected branchId: Property<number> = { name: 'bri' };
  protected currency: Property<string> = { name: 'cur' };
  protected endDate: Property<string> = { name: 'dtf' };
  protected endCountry: Property<string> = { name: 'ect' };
  protected endZoneId: Property<number> = { name: 'ezi' };
  protected markup: Property<number> = { name: 'mkp', value: 1 };
  protected markupId: Property<number> = { name: 'mki', value: 0 };
  protected packageGroup: Property<string> = {
    name: 'pkg',
    value: 'STANDALONE',
  };
  protected paxs: Property<string> = { name: 'pxs' };
  protected playerPriceWithoutTax: Property<number> = { name: 'pot' };
  protected playerPriceWithTax: Property<number> = { name: 'pwt' };
  protected productType: Property<string> = { name: 'prd', value: 'TRI' };
  protected productId: Property<number> = { name: 'pid' };
  protected searchDate: Property<string> = {
    name: 'sdt',
    value: new Date().toISOString().split('T')[0],
  };
  protected sellPriceWithoutTax: Property<number> = { name: 'sot' };
  protected sellPriceWithTax: Property<number> = { name: 'swt' };
  protected startDate: Property<string> = { name: 'dti' };
  protected playerId: Property<number> = { name: 'pla' };
  protected roomId: Property<number> = { name: 'rom' };

  static getRateTokenForOpportunities(
    travelInsurance: TravelInsurance,
    userCredential: Credential,
    queryParams: TravelInsuranceAvailQueryParamsDTO,
  ): string {
    const rateToken = new RateTokenConverter();

    rateToken.agentSign.value = userCredential.agentSign;
    rateToken.branchId.value = userCredential.branchId;
    rateToken.currency.value = travelInsurance.rate.currency;
    rateToken.endDate.value = travelInsurance.endDate;
    rateToken.endZoneId.value = queryParams.zoneId;
    rateToken.paxs.value = queryParams.paxs;
    rateToken.playerPriceWithoutTax.value =
      travelInsurance.rate.priceWithoutTax;
    rateToken.playerPriceWithTax.value = travelInsurance.rate.priceWithTax;
    rateToken.productId.value = travelInsurance.id;
    rateToken.sellPriceWithoutTax.value = travelInsurance.rate.priceWithoutTax;
    rateToken.sellPriceWithTax.value = travelInsurance.rate.priceWithTax;
    rateToken.startDate.value = travelInsurance.startDate;

    return rateToken.getAsBase64EncodedXmlAttribute();
  }

  static getRateTokenForHasAvail(
    travelInsurance: TravelInsurance,
    travelInsuranceRateToken: TravelInsuranceRateToken,
  ): string {
    const rateToken = new RateTokenConverter();

    rateToken.agentSign.value = travelInsuranceRateToken.agentSign;
    rateToken.branchId.value = travelInsuranceRateToken.branchId;
    rateToken.currency.value = travelInsurance.rate.currency;
    rateToken.endDate.value = travelInsuranceRateToken.endDate;
    rateToken.endCountry.value = travelInsuranceRateToken.endCountry;
    rateToken.endZoneId.value = travelInsuranceRateToken.endZoneId;
    rateToken.markup.value = travelInsuranceRateToken.markup;
    rateToken.markupId.value = travelInsuranceRateToken.markupId;
    rateToken.paxs.value = travelInsuranceRateToken.paxs;
    rateToken.playerPriceWithoutTax.value =
      travelInsuranceRateToken.playerPriceWithoutTax;
    rateToken.playerPriceWithTax.value =
      travelInsuranceRateToken.playerPriceWithTax;
    rateToken.productId.value = travelInsurance.id;
    rateToken.sellPriceWithoutTax.value =
      travelInsuranceRateToken.sellPriceWithoutTax;
    rateToken.sellPriceWithTax.value =
      travelInsuranceRateToken.sellPriceWithTax;
    rateToken.startDate.value = travelInsuranceRateToken.startDate;
    rateToken.playerId.value = travelInsuranceRateToken.playerId;
    rateToken.roomId.value = travelInsuranceRateToken.roomId;

    return rateToken.getAsBase64EncodedXmlAttribute();
  }

  protected getAsBase64EncodedXmlAttribute(): string {
    return Buffer.from(this.getAsXmlAttribute()).toString('base64');
  }

  protected getAsXmlAttribute(): string {
    let xml = '<rateToken ';

    for (const [_key, property] of Object.entries(this)) {
      if (property.value !== undefined) {
        xml += `${property.name}="${property.value}" `;
      }
    }

    xml += '/>';

    return xml;
  }
}
