import { Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as ConsulConnection from 'consul';

export default async function () {
  const logger = new Logger('ConsulConfig');
  const configService = new ConfigService();
  const path =
    process.env.CONSUL_PATH || configService.get<string>('consul.path');
  const host =
    process.env.CONSUL_HOST || configService.get<string>('consul.host');

  logger.log('Injecting Consul variables');

  const fromCallback = (
    fn: (cb: (err: any, data: any, res: any) => any) => any,
  ): Promise<any> =>
    new Promise((resolve, reject) => {
      try {
        return fn((err, data, res) => {
          if (err) {
            err.res = res;

            return reject(err);
          }

          return resolve([data, res]);
        });
      } catch (err) {
        return reject(err);
      }
    });

  const consul = ConsulConnection({
    host,
    promisify: fromCallback,
  });

  await Promise.all([
    consul.kv.get(`${path}/corp-sva-insurance-url`),
    consul.kv.get(`${path}/opportunities/look-for-opportunities`),
    consul.kv.get(`${path}/opportunities/opportunities-api-url`),
    consul.kv.get(`${path}/travel_voucher_api_url`),
    consul.kv.get(`${path}/benefits-ranking/default`),
    consul.kv.get(`${path}/benefits-ranking/custom`),
    consul.kv.get(`${path}/universal-assistence-url`),
    consul.kv.get(`${path}/universal-assistence-logo-url`),
    consul.kv.get(`${path}/country-code-translator`),
    consul.kv.get(`${path}/orchestrator_operations_api_url`),
  ]).then((values) => {
    process.env.CORP_SVA_INSURANCE_API_URL = values[0][0]?.Value;
    process.env.LOOK_FOR_OPPORTUNITIES = values[1][0]?.Value;
    process.env.OPPORTUNITIES_API_URL = values[2][0]?.Value;
    process.env.TRAVEL_VOUCHER_API_URL = values[3][0]?.Value;
    process.env.BENEFITS_RANKING_DEFAULT = values[4][0]?.Value;
    process.env.BENEFITS_RANKING_CUSTOM = values[5][0]?.Value;
    process.env.UNIVERSAL_ASSISTANCE_URL = values[6][0]?.Value;
    process.env.UNIVERSAL_ASSISTANCE_LOGO_URL = values[7][0]?.Value;
    process.env.COUNTRY_CODE_TRANSLATOR = values[8][0]?.Value;
    process.env.ORCHESTRATOR_OPERATIONS_API_URL = values[9][0]?.Value;
  });
}
