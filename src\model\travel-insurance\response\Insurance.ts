import { ApiProperty } from '@nestjs/swagger';
import { Promotion } from '../../opportunities/promotion/promotion';
import { Benefit } from '../benefit';
import { Rate } from '../rate';
import { InsuranceCommissionResponse } from './commission/insurance-commission.response';

export class Insurance {
  @ApiProperty()
  id: number;

  @ApiProperty()
  insuranceToken: string;

  @ApiProperty({ type: Benefit, isArray: true })
  benefits: Benefit[];

  @ApiProperty()
  startDate: string;

  @ApiProperty()
  endDate: string;

  @ApiProperty()
  passengers: number;

  @ApiProperty()
  name: string;

  @ApiProperty()
  description: string;

  @ApiProperty()
  additionalDescription: string;

  @ApiProperty()
  company: string;

  @ApiProperty()
  rates: Rate[];

  @ApiProperty()
  taxes: string[];

  @ApiProperty()
  international: boolean;

  @ApiProperty()
  cancelable: boolean;

  @ApiProperty()
  promotion: Promotion;

  @ApiProperty()
  valueToFilter: number;

  @ApiProperty()
  rateToken: string;

  @ApiProperty()
  cancelation: boolean;

  @ApiProperty()
  currency: string;

  @ApiProperty()
  price: number;

  key: string;

  contents: any[];

  @ApiProperty({ type: InsuranceCommissionResponse })
  commission: InsuranceCommissionResponse;

  @ApiProperty()
  eventCode: string;
  imageCompany: string;

  @ApiProperty()
  acceptableDocuments: string[];
}
