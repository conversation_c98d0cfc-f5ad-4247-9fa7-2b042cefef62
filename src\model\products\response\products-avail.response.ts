export class ProductsAvailResponse {
  products: Product[];
  meta: Meta;
}

export interface Product {
  id: string;
  externalId: string;
  agreement: string;
  name: string;
  description: string;
  company: string;
  type: string;
  provider: string;
  category: Category;
  preferential: boolean;
  saleAllowed: boolean;
  links: Link[];
  prices: Price[];
  contents: Content[];
  acceptableDocuments: string[];
}

export interface Category {
  code: string;
  name: string;
}

export interface Link {
  name: string;
  method: string;
  href: string;
  active: boolean;
}

export interface Price {
  currency: string;
  rates: Rate[];
  cancellationPolicy: CancellationPolicy[];
}

export interface Rate {
  rph: number;
  packageGroup: string;
  rateToken: string;
  startDate: string;
  endDate: string;
  baseAmount: number;
  priceWithTax: number;
  priceWithoutTax: number;
  pricePerDayWithTax: number;
  pricePerDayWithoutTax: number;
  pricePerPaxWithTax: number;
  pricePerPaxWithoutTax: number;
  taxes: any[];
}

export interface CancellationPolicy {
  nonRefundable: boolean;
}

export interface Content {
  name: string;
  quantity: number;
  items: Item[];
}

export interface Item {
  code: string;
  name: string;
  description?: string;
  amount: number;
  min: number;
  max: number;
}

export interface Meta {
  providerTypes: string[];
  productTypes: string[];
  filters: Filters;
  paging: Paging;
}

export interface Filters {
  categories: Category2[];
}

export interface Category2 {
  code: string;
  name: string;
  count: number;
}

export interface Paging {
  currentPage: number;
  pageCount: number;
  pageSize: number;
  total: number;
  sortOrder: string;
}
