import {
  Controller,
  Body,
  Post,
  Query,
  UseFilters,
  UseInterceptors,
  Param,
} from '@nestjs/common';
import { ApiHeader, ApiTags } from '@nestjs/swagger';
import { ErrorFilter } from '../../../error/error.filter';
import { ContextInterceptor } from '../../../config/context/interceptor/context-interceptor';
import { RequestHeaders } from '../../../config/context/model/request-headers.enum';
import { InsurancesWebService } from '../../../service/insurances-web/insurances-web.service';
import { TravelInsuranceAvailResponse } from 'src/model/travel-insurance/response/travel-insurance-avail.response';
import { InsuranceRequestBody } from 'src/model/opportunities/request/insurance-request-body';
import { TravelInsuranceHasavailResponse } from 'src/model/travel-insurance/response/travel-insurance-hasavail.response';

@Controller('insurances/web')
@ApiTags('Insurances-web')
@UseFilters(new ErrorFilter())
@UseInterceptors(ContextInterceptor)
@ApiHeader({ name: RequestHeaders.SEC_USER_TOKEN, required: true })
@ApiHeader({ name: RequestHeaders.TRANSACTION_ID, required: true })
export class InsurancesWebController {
  constructor(private insuranceService: InsurancesWebService) {}

  @Post('/avail')
  async avail(
    @Query() params: any,
    @Body() requestBody: InsuranceRequestBody,
  ): Promise<TravelInsuranceAvailResponse> {
    return this.insuranceService.getAvails(params, requestBody);
  }

  @Post('hasavail/:rateToken')
  async hasAvail(
    @Param('rateToken') rateToken: string,
    @Body() requestBody: InsuranceRequestBody,
  ): Promise<TravelInsuranceHasavailResponse> {
    return this.insuranceService.getHasAvail(rateToken, requestBody);
  }
}
