import { Statement } from './statement';
import { PromoId } from './promo-id';
import { ApiProperty } from '@nestjs/swagger';

export class Promotion {
  @ApiProperty({ type: Statement, isArray: true })
  statements: Statement[];

  @ApiProperty()
  priceWithTax: number;

  @ApiProperty()
  priceWithoutTax: number;

  @ApiProperty()
  pricePerDayWithTax: number;

  @ApiProperty()
  pricePerDayWithoutTax: number;

  @ApiProperty()
  percentage: number;

  @ApiProperty()
  discountApplied: string;

  @ApiProperty({ type: PromoId, isArray: true })
  promoIds: PromoId[];

  @ApiProperty()
  hasCombo: boolean;

  @ApiProperty()
  rateToken: string;
}
