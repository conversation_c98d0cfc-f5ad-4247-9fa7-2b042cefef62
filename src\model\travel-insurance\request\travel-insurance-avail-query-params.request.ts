import { ApiHideProperty, ApiProperty } from '@nestjs/swagger';
import {
  IsDateString,
  IsEnum,
  IsNotEmpty,
  IsNumberString,
  IsOptional,
  IsString,
} from 'class-validator';
import { TravelInsuranceOrder } from '../enums/TravelInsuranceOrder';
import { TravelInsuranceDetailQueryParamsDTO } from './travel-insurance-detail-query-params.request';

export class TravelInsuranceAvailQueryParamsDTO {
  @IsDateString(
    {},
    {
      message:
        'startDate must be a valid ISO 8601 date string following the pattern yyyy-MM-dd',
    },
  )
  @ApiProperty({
    type: Date,
    format: 'yyyy-MM-dd',
  })
  startDate: string;

  @IsDateString(
    {},
    {
      message:
        'endDate must be a valid ISO 8601 date string following the pattern yyyy-MM-dd',
    },
  )
  @ApiProperty({
    type: Date,
    format: 'yyyy-MM-dd',
  })
  endDate: string;

  @IsNumberString({}, { message: 'zoneId must be a number' })
  @ApiProperty()
  zoneId: number;

  @IsNotEmpty()
  @ApiProperty()
  paxs: string;

  @ApiHideProperty()
  country: string;

  @IsOptional()
  @ApiProperty({ required: false })
  id: number;

  @IsOptional()
  @IsEnum(TravelInsuranceOrder, {
    message: `orderBy must be a valid enum value, acceptables values: ${Object.values(
      TravelInsuranceOrder,
    )}`,
  })
  @ApiProperty({
    enum: TravelInsuranceOrder,
    required: false,
  })
  orderBy: TravelInsuranceOrder;

  @IsOptional()
  @IsNumberString({}, { message: 'minValue must be a number' })
  @ApiProperty({ required: false })
  minValue: number;

  @IsOptional()
  @IsString()
  @ApiProperty({ required: false })
  sortOrder: string;

  @IsOptional()
  @IsNumberString({}, { message: 'maxValue must be a number' })
  @ApiProperty({ required: false })
  maxValue: number;

  @IsOptional()
  @ApiProperty({ required: false, isArray: true })
  name: string[];

  public static hydrateDTO(
    params: TravelInsuranceAvailQueryParamsDTO,
  ): TravelInsuranceAvailQueryParamsDTO {
    const travelInsuranceAvailQueryParamsDTO =
      new TravelInsuranceAvailQueryParamsDTO();
    travelInsuranceAvailQueryParamsDTO.startDate = params.startDate;
    travelInsuranceAvailQueryParamsDTO.endDate = params.endDate;
    travelInsuranceAvailQueryParamsDTO.zoneId = params.zoneId;
    travelInsuranceAvailQueryParamsDTO.paxs = params.paxs;
    travelInsuranceAvailQueryParamsDTO.country = params.country;
    travelInsuranceAvailQueryParamsDTO.id = params.id;
    travelInsuranceAvailQueryParamsDTO.orderBy = params.orderBy;
    travelInsuranceAvailQueryParamsDTO.minValue = params.minValue;
    travelInsuranceAvailQueryParamsDTO.maxValue = params.maxValue;

    let name: string[];

    if (params.name) {
      if (Array.isArray(params.name)) {
        name = params.name;
      } else {
        name = String(params.name).split(',');
      }
    }

    travelInsuranceAvailQueryParamsDTO.name = name;

    return travelInsuranceAvailQueryParamsDTO;
  }

  public static fromTravelInsuranceDetailQueryParamsDTO(
    params: TravelInsuranceDetailQueryParamsDTO,
  ): TravelInsuranceAvailQueryParamsDTO {
    const travelInsuranceAvailQueryParamsDTO =
      new TravelInsuranceAvailQueryParamsDTO();
    travelInsuranceAvailQueryParamsDTO.startDate = params.startDate;
    travelInsuranceAvailQueryParamsDTO.endDate = params.endDate;
    travelInsuranceAvailQueryParamsDTO.zoneId = params.zoneId;
    travelInsuranceAvailQueryParamsDTO.paxs = params.paxs;

    return travelInsuranceAvailQueryParamsDTO;
  }

  public toGetAvail(): any {
    const defaultParams = this.createDefaulParamsToAvail();

    if (this.id) {
      return {
        id: this.id,
        ...defaultParams,
      };
    }

    return defaultParams;
  }

  public isItToFilter(): boolean {
    return !!this.minValue || !!this.maxValue || this.containsName();
  }

  public isItToOrder(): boolean {
    return !!this.orderBy;
  }

  public minValueIsGreaterThanMaxValue(): boolean {
    if (this.minValue && this.maxValue) {
      return Number(this.minValue) > Number(this.maxValue);
    }
    return false;
  }

  public containsName(): boolean {
    return this.name && this.name.length > 0;
  }

  private createDefaulParamsToAvail(): any {
    return {
      startDate: this.startDate,
      endDate: this.endDate,
      paxs: this.paxs,
      country: this.country,
      endZoneId: this.zoneId,
      packageGroup: 'STANDALONE',
      sortOrder: 'RANKING',
    };
  }
}
