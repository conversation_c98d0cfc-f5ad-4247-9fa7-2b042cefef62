import { ApiProperty } from '@nestjs/swagger';
import { Promotion } from '../opportunities/promotion/promotion';
import { Benefit } from './benefit';
import { Rate } from './rate';
import { InsuranceCommissionResponse } from './response/commission/insurance-commission.response';

export class TravelInsurance {
  @ApiProperty()
  id: number;

  @ApiProperty()
  insuranceToken: string;

  @ApiProperty({ type: Benefit, isArray: true })
  benefits: Benefit[];

  @ApiProperty()
  startDate: string;

  @ApiProperty()
  endDate: string;

  @ApiProperty()
  passengers: number;

  @ApiProperty()
  name: string;

  @ApiProperty()
  description: string;

  @ApiProperty()
  additionalDescription: string;

  @ApiProperty()
  company: string;

  @ApiProperty()
  rate: Rate;

  @ApiProperty()
  taxes: string[];

  @ApiProperty()
  international: boolean;

  @ApiProperty()
  cancelable: boolean;

  @ApiProperty()
  promotion: Promotion;

  @ApiProperty()
  valueToFilter: number;

  @ApiProperty()
  agreement: string;

  key: string;

  @ApiProperty({ type: InsuranceCommissionResponse })
  commission: InsuranceCommissionResponse;

  imageCompany: string;

  @ApiProperty()
  acceptableDocuments: string[];
}
