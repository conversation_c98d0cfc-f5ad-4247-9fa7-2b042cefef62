import { HttpService } from '@nestjs/axios';
import { Injectable, Logger } from '@nestjs/common';
import { ContextService } from 'src/config/context/service/context.service';
import { EndpointConfig } from 'src/config/endpoint/endpoint.config';
import { TravelInsuranceCountryResponseDTO } from '../model/travel-insurance/response/travel-insurance-country.response';

@Injectable()
export class TravelVoucherApiBridge {
  private path: string;
  private readonly logger: Logger;

  constructor(
    private readonly endpointConfig: EndpointConfig,
    private readonly httpService: HttpService,
    private readonly contextService: ContextService,
  ) {
    this.path = 'country/state/city';
    this.logger = new Logger(TravelVoucherApiBridge.name);
  }

  async getCountry(zoneId: number): Promise<TravelInsuranceCountryResponseDTO> {
    const uri = `${this.endpointConfig.getTravelVoucherUrl()}/${this.path}`;
    const context = this.contextService.current();

    this.logger.log(`Executing http get request at ${uri}`);

    const { data } =
      await this.httpService.axiosRef.get<TravelInsuranceCountryResponseDTO>(
        uri,
        {
          headers: context.getCommomRequestHeaders(),
          params: { zoneId },
        },
      );

    return data;
  }
}
