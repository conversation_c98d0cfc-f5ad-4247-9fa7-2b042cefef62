import { Context } from "src/config/context/model/context";
import { Credential } from "src/config/context/model/credential";

export class ContextMock {

    private static TRANSACTION_ID = "d8a50e95-1fd7-fe4d-4c3a-88f0b91b5865-1628197176343";
    private static USER_TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjcmVkZW50aWFsIjp7InBlcnNvbklkIjoxMjk4Nzk0Ny" +
    "widXNlcklkIjoxMzUwNDcsIm5hbWUiOiJST0RSSUdPIEdPTUVTIERFIE9MSVZFSVJBIiwiY3BmIjoiMjYwN" +
    "DEzMDE4MTciLCJicmFuY2hJZCI6MTAwMCwiYWdlbnRTaWduIjoiTE9KIiwidXNlciI6Ik1UWkNQRDEyMzYi" +
    "LCJ1c2VyVHlwZSI6IlZFTkRPUiJ9LCJzeXN0ZW1zIjpbeyJzeXN0ZW1Db2RlIjozLCJjbGllbnRJRCI6MzA" +
    "5MTIyMzM0NzA1NjE3NjQsImV4cGlyZXNPbiI6IjIwMTctMTAtMjRUMTQ6NDk6MjEuMDAwLTAyOjAwIiwiYW" +
    "N0aXZlIjoiUyAifSx7InN5c3RlbUNvZGUiOjI0LCJjbGllbnRJRCI6MzA5MTIyMzM0NzA1NjE3NjQsImV4cG" +
    "lyZXNPbiI6IjIwMTctMTAtMjRUMTQ6NDk6MjEuMDAwLTAyOjAwIiwiYWN0aXZlIjoiUyAifV0sImlhdCI6MT" +
    "YwODg2Mjg2Mn0.A_yfRkAPWhECgoP4hWShpRLh8RmDYN703fDk9NJDsf8";
    
    private constructor() {}
    
    static getContext(): Context {

        const credential: Credential = new Credential(1, 1, 'UserTest', '<EMAIL>', '99999999999', 1000,'WEB', 'test', '');
        return new Context(this.USER_TOKEN, this.TRANSACTION_ID, credential);	
    } 
}