import { ApiProperty } from '@nestjs/swagger';
import { GroupResponse } from './group.response';
import { LinkResponse } from './link.response';
import { MetricResponse } from './metric/metric.response';

export class MetaAvailResponse {
  @ApiProperty()
  highestValue: number;

  @ApiProperty({ type: GroupResponse, isArray: true })
  groups: GroupResponse[];

  @ApiProperty({ type: LinkResponse, isArray: true })
  links: LinkResponse[];

  @ApiProperty({ type: MetricResponse, isArray: true })
  metrics: MetricResponse;

  @ApiProperty()
  count: number;
}
