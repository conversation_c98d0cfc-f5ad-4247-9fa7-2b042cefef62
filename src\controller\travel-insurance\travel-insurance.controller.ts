import {
  Body,
  Controller,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Get,
  Query,
  UseFilters,
  UseInterceptors,
} from '@nestjs/common';
import { ApiHeader, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { ContextInterceptor } from 'src/config/context/interceptor/context-interceptor';
import { RequestHeaders } from 'src/config/context/model/request-headers.enum';
import { ErrorFilter } from 'src/error/error.filter';
import { InsuranceRequestBody } from 'src/model/opportunities/request/insurance-request-body';
import { TravelInsuranceAvailQueryParamsDTO } from 'src/model/travel-insurance/request/travel-insurance-avail-query-params.request';
import { TravelInsuranceAvailResponse } from 'src/model/travel-insurance/response/travel-insurance-avail.response';
import { TravelInsuranceHasavailResponse } from 'src/model/travel-insurance/response/travel-insurance-hasavail.response';
import { TravelInsuranceService } from 'src/service/travel-insurance/travel-insurance.service';
import { HttpStatusMessage } from 'src/util/http-status-message';

@Controller('insurances')
@ApiTags('Insurances')
@UseFilters(new ErrorFilter())
@UseInterceptors(ContextInterceptor)
@ApiHeader({ name: RequestHeaders.SEC_USER_TOKEN, required: true })
@ApiHeader({ name: RequestHeaders.TRANSACTION_ID, required: true })
export class TravelInsuranceController {
  constructor(private travelInsuranceService: TravelInsuranceService) {}

  async delay(miliseconds: number) {
    return new Promise((resolve) => {
      setTimeout(resolve, miliseconds);
    });
  }

  @Get('timeout/:miliseconds')
  async checkTimeout(
    @Param('miliseconds') miliseconds: number,
  ): Promise<string> {
    await this.delay(miliseconds);
    return 'OK';
  }

  @Post('avail')
  @ApiOperation({ description: 'Avail -> query travel insurances by criteria' })
  @HttpCode(HttpStatus.OK)
  @ApiResponse({
    status: 200,
    description: HttpStatusMessage.ok.description,
    type: TravelInsuranceAvailResponse,
  })
  @ApiResponse({
    status: 400,
    description: HttpStatusMessage.badRequest.description,
  })
  @ApiResponse({
    status: 401,
    description: HttpStatusMessage.unauthorized.description,
  })
  @ApiResponse({
    status: 403,
    description: HttpStatusMessage.forbidden.description,
  })
  async avail(
    @Query() params: TravelInsuranceAvailQueryParamsDTO,
    @Body() requestBody: InsuranceRequestBody,
  ): Promise<TravelInsuranceAvailResponse> {
    return await this.travelInsuranceService.avail(
      TravelInsuranceAvailQueryParamsDTO.hydrateDTO(params),
      requestBody,
    );
  }

  @Post('hasavail/:rateToken')
  @ApiOperation({
    description: 'Hasavail -> check travel insurance availability',
  })
  @HttpCode(HttpStatus.OK)
  @ApiResponse({
    status: 200,
    description: HttpStatusMessage.ok.description,
    type: TravelInsuranceHasavailResponse,
  })
  @ApiResponse({
    status: 400,
    description: HttpStatusMessage.badRequest.description,
  })
  @ApiResponse({
    status: 401,
    description: HttpStatusMessage.unauthorized.description,
  })
  @ApiResponse({
    status: 403,
    description: HttpStatusMessage.forbidden.description,
  })
  @ApiResponse({
    status: 404,
    description: HttpStatusMessage.notFound.description,
  })
  async hasavail(
    @Param('rateToken') rateToken: string,
    @Body() requestBody: InsuranceRequestBody,
  ): Promise<TravelInsuranceHasavailResponse> {
    return await this.travelInsuranceService.hasavail(rateToken, requestBody);
  }
}
