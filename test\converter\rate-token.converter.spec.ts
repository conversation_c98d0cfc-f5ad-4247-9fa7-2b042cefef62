import { Credential } from 'src/config/context/model/credential';
import { RateTokenConverter } from 'src/converter/rate-token.converter';
import { TravelInsuranceRateToken } from 'src/model/rate-token/travel-insurance-rate-token';
import { Benefit } from 'src/model/travel-insurance/benefit';
import { TravelInsuranceAvailQueryParamsDTO } from 'src/model/travel-insurance/request/travel-insurance-avail-query-params.request';
import { Rate } from 'src/model/travel-insurance/rate';
import { TravelInsurance } from 'src/model/travel-insurance/travel-insurance';

describe('RateTokenConverter', () => {      

    const credential = new Credential(1, 123, 'MTZ123', '<EMAIL>', '99999999999', 1000, 'WEB', 'Shakira', '');
    const queryParams = new TravelInsuranceAvailQueryParamsDTO();
    const travelInsurance = new TravelInsurance();
    const benefitOne = new Benefit();
    const benefitTwo = new Benefit();    
    const rate = new Rate();

    queryParams.zoneId = 13601;
    queryParams.paxs = '30,30';

    benefitOne.name = 'Despesas Médicas E/Ou Hospitalares em Viagem';
    benefitOne.value = 'R$ 100.000,00';
    benefitOne.id = 2699;
    benefitOne.minAge = 0;
    benefitOne.maxAge = 80;

    benefitTwo.name = 'Despesas médicas por covid-19';
    benefitTwo.value = 'R$ 56.468,00';
    benefitTwo.id = 2782;
    benefitTwo.minAge = 0;
    benefitTwo.maxAge = 80;

    rate.currency = 'BRL';
    rate.pricePerPaxWithTax = 417.14;
    rate.pricePerPaxWithoutTax = 417.14;
    rate.priceWithTax = 208.57;
    rate.priceWithoutTax = 208.57;
        
    travelInsurance.id = 607347;
    travelInsurance.insuranceToken = '********************************************';
    travelInsurance.benefits = [];
    travelInsurance.startDate = '2022-10-10';
    travelInsurance.endDate = '2022-10-10';
    travelInsurance.passengers = 2;
    travelInsurance.name = 'Nacional + -O2021 TESTE';
    travelInsurance.description = 'Nacional + -O2021 TESTE';
    travelInsurance.company = 'TRAVELACE'        
    travelInsurance.cancelable = true;
    
    travelInsurance.benefits = [benefitOne, benefitTwo];
    travelInsurance.rate = rate;          

    it('must create rate token for opportunities ', () => {                
        const result = RateTokenConverter.getRateTokenForOpportunities(travelInsurance, credential, queryParams);    
        
        expect(result).toBeTruthy();                                            
    }); 
    
    it('must create rate token for hasavail ', () => {                

        const travelInsuranceRateToken = new TravelInsuranceRateToken();
        travelInsuranceRateToken.keyRateToken = '********************************************';
        travelInsuranceRateToken.priceWithoutTax = 417.14;
        travelInsuranceRateToken.priceWithTax = 417.14;
        travelInsuranceRateToken.taxes = [];
        travelInsuranceRateToken.roomId = 607347;
        travelInsuranceRateToken.startCountry = 'BR';
        travelInsuranceRateToken.startState = 'SP';
        travelInsuranceRateToken.startZoneId = 9583
        travelInsuranceRateToken.endCountry = 'BR';
        travelInsuranceRateToken.endContinent = '';
        travelInsuranceRateToken.packageGroup = 'STANDALONE';
        travelInsuranceRateToken.agentSign = 'WEB';
        travelInsuranceRateToken.branchId = 1000;
        travelInsuranceRateToken.searchDate = '2022-10-16';
        travelInsuranceRateToken.channelManagerId = '1';
        travelInsuranceRateToken.paxs = '30,30';
        travelInsuranceRateToken.playerPriceWithoutTax = 146;
        travelInsuranceRateToken.playerPriceWithTax = 146;
        travelInsuranceRateToken.startDate = '2022-10-17';
        travelInsuranceRateToken.endDate = '2022-10-17';
        travelInsuranceRateToken.markupId = 290522;
        travelInsuranceRateToken.markup = 0.350;
        travelInsuranceRateToken.productType = 'TRI';
        travelInsuranceRateToken.sellPriceWithTax = 417.14;
        travelInsuranceRateToken.sellPriceWithoutTax = 417.14;
        travelInsuranceRateToken.personPayment = '';
        travelInsuranceRateToken.endZoneId = 13601;
        travelInsuranceRateToken.playerId = 19048329;

        const result = RateTokenConverter.getRateTokenForHasAvail(travelInsurance, travelInsuranceRateToken);    
        
        expect(result).toBeTruthy();                                            
    });  

});