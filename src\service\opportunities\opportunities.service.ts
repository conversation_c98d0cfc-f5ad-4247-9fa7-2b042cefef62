import { HttpStatus, Injectable, Logger } from '@nestjs/common';
import { OpportunitiesApiBridge } from 'src/bridge/opportunities-api.bridge';
import { OpportunitiesConfig } from 'src/config/opportunities/opportunities.config';
import { MetricCalculate } from 'src/model/metric/metric-calculate';
import { OpportunitiesItemRequest } from 'src/model/opportunities/request/opportunities-item.request';
import { OpportunitiesRequest } from 'src/model/opportunities/request/opportunities.request';
import { OpportunitiesItemResponse } from 'src/model/opportunities/response/opportunities-item.response';
import { OpportunitiesResponse } from 'src/model/opportunities/response/opportunities.response';
import { TravelInsurance } from 'src/model/travel-insurance/travel-insurance';

@Injectable()
export class OpportunitiesService {
  private readonly logger: Logger;

  constructor(
    private readonly bridge: OpportunitiesApiBridge,
    private readonly config: OpportunitiesConfig,
  ) {
    this.logger = new Logger(OpportunitiesService.name);
  }

  async applyOpportunitiesIfAnyAndReturnTravelInsurances(
    travelInsurances: TravelInsurance[],
    selectedItems: OpportunitiesItemRequest[],
    metric: MetricCalculate,
  ): Promise<TravelInsurance[]> {
    await this.applyOpportunitiesIfAny(travelInsurances, selectedItems, metric);

    return travelInsurances;
  }

  async applyOpportunitiesIfAnyAndReturnTravelInsurance(
    travelInsurance: TravelInsurance,
    selectedItems: OpportunitiesItemRequest[],
    metric: MetricCalculate,
  ): Promise<TravelInsurance> {
    await this.applyOpportunitiesIfAny(
      [travelInsurance],
      selectedItems,
      metric,
    );

    return travelInsurance;
  }

  private async applyOpportunitiesIfAny(
    travelInsurances: TravelInsurance[],
    selectedItems: OpportunitiesItemRequest[],
    metric: MetricCalculate,
  ): Promise<void> {
    if (!this.config.isItToLookForOpportunities()) {
      metric.start();

      const opportunitiesRequest = OpportunitiesRequest.fromTravelInsurances(
        travelInsurances,
        selectedItems,
      );
      const opportunities = await this.hasPromotion(
        opportunitiesRequest,
        metric,
      );

      this.applyPromotions(travelInsurances, opportunities, metric);

      metric.end();
    }
  }

  private async hasPromotion(
    opportunitiesRequest: OpportunitiesRequest,
    metric: MetricCalculate,
  ): Promise<OpportunitiesResponse> {
    let opportunitiesResponse = undefined;

    try {
      opportunitiesResponse = await this.bridge.hasOpportunities(
        opportunitiesRequest,
      );
    } catch (error) {
      this.logger.warn(
        'Error getting promotions, returning travel insurances without applying promotions',
        error,
      );

      let status = error.response?.status;

      if (!status) {
        status = HttpStatus.INTERNAL_SERVER_ERROR;
      }

      metric.sendError(status, error.message);
    }

    return opportunitiesResponse;
  }

  private applyPromotions(
    travelInsurances: TravelInsurance[],
    opportunities: OpportunitiesResponse,
    metric: MetricCalculate,
  ): void {
    if (this.noHasPromotionsForTheInsurances(opportunities)) {
      return;
    }

    if (this.promotionsHasErrors(opportunities)) {
      const error = opportunities.meta.error.join('; ');
      metric.sendError(HttpStatus.INTERNAL_SERVER_ERROR, error);
      return;
    }

    opportunities.availableItems
      .filter((opportunity) => opportunity.promotion != null)
      .forEach((opportunity) =>
        this.updateTravelInsurance(travelInsurances, opportunity),
      );
  }

  private noHasPromotionsForTheInsurances(
    opportunities: OpportunitiesResponse,
  ): boolean {
    return !opportunities || !opportunities.availableItems;
  }

  private promotionsHasErrors(opportunities: OpportunitiesResponse): boolean {
    return (
      opportunities.meta &&
      opportunities.meta.error &&
      opportunities.meta.error.length > 0
    );
  }

  private updateTravelInsurance(
    travelInsurances: TravelInsurance[],
    opportunity: OpportunitiesItemResponse,
  ): void {
    const travelInsurance = travelInsurances.filter(
      (travelInsurance) => travelInsurance.id === opportunity.rph,
    )[0];

    if (travelInsurance) {
      this.setInsurancePromotion(travelInsurance, opportunity);
    }
  }

  private setInsurancePromotion(
    travelInsurance: TravelInsurance,
    opportunity: OpportunitiesItemResponse,
  ): void {
    // Formata percentage: 0.123 → 12.3, 0.1 → 10, 0.05 → 5
    const percentage =
      Math.round(opportunity.promotion.percentage * 10000) / 100;
    const percentageString = percentage.toFixed(2).replace(/\.?0+$/, '');
    opportunity.promotion.percentage = parseFloat(percentageString);

    travelInsurance.insuranceToken = opportunity.rateToken;
    travelInsurance.promotion = opportunity.promotion;
    travelInsurance.promotion.rateToken = opportunity.rateToken;
    travelInsurance.promotion.hasCombo = opportunity.hasCombo;
  }
}
