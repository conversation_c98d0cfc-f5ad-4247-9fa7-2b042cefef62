export class TravelInsuranceRateToken {
  keyRateToken: string;
  priceWithoutTax: number;
  priceWithTax: number;
  taxes: string[];
  roomId: number;
  startCountry: string;
  startState: string;
  startZoneId: number;
  endCountry: string;
  endContinent: string;
  packageGroup: string;
  agentSign: string;
  branchId: number;
  searchDate: string;
  channelManagerId: string;
  paxs: string;
  playerPriceWithoutTax: number;
  playerPriceWithTax: number;
  startDate: string;
  endDate: string;
  markupId: number;
  markup: number;
  productType: string;
  sellPriceWithTax: number;
  sellPriceWithoutTax: number;
  personPayment: string;
  endZoneId: number;
  playerId: number;
}
