import {
  CallHandler,
  ExecutionContext,
  Injectable,
  Logger,
  NestInterceptor,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Observable, tap } from 'rxjs';
import { RequiredParameterNotPresentException } from 'src/error/required-parameter-not-present-exception';
import { Context } from '../model/context';
import { Credential } from '../model/credential';
import { RequestHeaders } from '../model/request-headers.enum';
import { ContextService } from '../service/context.service';

@Injectable()
export class ContextInterceptor implements NestInterceptor {
  private readonly logger: Logger;

  constructor(
    private readonly service: ContextService,
    private readonly jwtService: JwtService,
  ) {
    this.logger = new Logger(ContextInterceptor.name);
  }

  intercept(
    executionContext: ExecutionContext,
    next: CallHandler<any>,
  ): Observable<any> | Promise<Observable<any>> {
    const now = Date.now();
    const request = executionContext.switchToHttp().getRequest();
    const { headers, method, url } = request;
    const response = executionContext.switchToHttp().getResponse();

    response.setTimeout(60000);

    const secUserToken: string = headers[RequestHeaders.SEC_USER_TOKEN];
    const transactionId: string = headers[RequestHeaders.TRANSACTION_ID];
    const gtwPricing: string = headers[RequestHeaders.GTW_PRICING];

    this.checkMandatoryHeaders(secUserToken, transactionId);

    const decodedJwt = this.jwtService.decode(secUserToken) as Context;
    const context = new Context(
      secUserToken,
      transactionId,
      Credential.hydrate(decodedJwt.credential),
      gtwPricing,
    );

    this.service.set(context);

    // Log do curl da requisição
    this.printCurlCommand(request);

    return next.handle().pipe(
      tap(() => {
        const { statusCode } = executionContext.switchToHttp().getResponse();
        const durationInMilliseconds = Date.now() - now;

        const data = {
          operation: 'request',
          transactionId,
          durationInMilliseconds,
          path: url,
          statusCode,
          method,
        };

        this.logger.log(
          `Request to ${url} executed in ${durationInMilliseconds} ms data: ${JSON.stringify(
            data,
          )}`,
        );

        this.service.clear();
      }),
    );
  }

  private printCurlCommand(request: any): string {
    try {
      const { method, url, headers } = request;
      const baseUrl = `${request.protocol}://${request.get('host')}`;
      const fullUrl = `${baseUrl}${url}`;

      let curlCommand = `curl -X ${method} '${fullUrl}'`;

      // Adiciona headers
      Object.entries(headers).forEach(([key, value]) => {
        if (key !== 'host') {
          curlCommand += ` -H '${key}: ${value}'`;
        }
      });

      // Adiciona body se existir
      if (request.body && Object.keys(request.body).length > 0) {
        curlCommand += ` -d '${JSON.stringify(request.body)}'`;
      }

      this.logger.log(`
        ================== CURL REQUEST FRONT ==================
        ${curlCommand}
        ========================================================
        `);

      return curlCommand;
    } catch (error) {
      return "Erro ao montar o comando curl: " + error.message;
    }
  }

  private checkMandatoryHeaders(
    secUserToken: string,
    transactionId: string,
  ): void {
    const errors: string[] = [];

    if (!secUserToken) {
      errors.push(RequestHeaders.SEC_USER_TOKEN);
    }

    if (!transactionId) {
      errors.push(RequestHeaders.TRANSACTION_ID);
    }

    if (errors.length > 0) {
      throw new RequiredParameterNotPresentException(
        `Required request header ${errors.join(' / ')} is not present`,
      );
    }
  }
}
