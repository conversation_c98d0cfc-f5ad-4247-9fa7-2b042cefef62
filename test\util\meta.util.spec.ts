import { MetricResultResponse } from 'src/model/travel-insurance/response/metric/metric-result.response';
import { MetaUtil } from 'src/util/meta.util';

describe('MetaUtil', () => {

    it('must apply metrics', () => {      
        const metric = new MetricResultResponse();
        metric.duration = 100;
        metric.status = 200;

        const metricToPromotion = new MetricResultResponse();
        metricToPromotion.duration = 50;
        metricToPromotion.status = 200;

        const result = MetaUtil.applyMetrics(metric, metricToPromotion);
                
        expect(result).toBeTruthy();                      
        expect(result.promotions.length).toEqual(1);                      
    });

    it('must apply metrics without metric to promotion', () => {      
        const metric = new MetricResultResponse();
        metric.duration = 100;
        metric.status = 200;
               
        const result = MetaUtil.applyMetrics(metric, undefined);

        expect(result).toBeTruthy();                      
        expect(result.promotions.length).toEqual(0);
    });
 
});