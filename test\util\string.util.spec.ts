import { StringUtil } from 'src/util/string.util';

describe('StringUtil', () => {      

    it('must return valid numbers ', () => {                
        expect(StringUtil.toNumber('R$ 23.997.123')).toEqual(23997123);
        expect(StringUtil.toNumber('R$ 23,997,123')).toEqual(23997123);
        expect(StringUtil.toNumber('R$ 23,997,123,10')).toEqual(23997123.10);
        expect(StringUtil.toNumber('R$ 23,997,123,100')).toEqual(23997123100);
        expect(StringUtil.toNumber('R$ 23,997,123.100')).toEqual(23997123.10);
        expect(StringUtil.toNumber('R$ 1.000.000')).toEqual(1000000);
        expect(StringUtil.toNumber('R$ 1.000.000,00')).toEqual(1000000);
        expect(StringUtil.toNumber('R$ 1,000,000')).toEqual(1000000);
        expect(StringUtil.toNumber('R$ 1,000,000.00')).toEqual(1000000);        
        expect(StringUtil.toNumber('R$ 1.999,88')).toEqual(1999.88);                                            
        expect(StringUtil.toNumber('R$ 3,534.01')).toEqual(3534.01);
        expect(StringUtil.toNumber('R$ 183,312,534.03')).toEqual(183312534.03);
        expect(StringUtil.toNumber('R$ 1.555.444,57?!@&*(/')).toEqual(1555444.57);        
        expect(StringUtil.toNumber('SIM 1.555.444,578893 NOT')).toEqual(1555444.58);
        expect(StringUtil.toNumber('$ 3,222.123456')).toEqual(3222.12);
        expect(StringUtil.toNumber('10')).toEqual(10.0);
        expect(StringUtil.toNumber('0,1')).toEqual(0.1);
        expect(StringUtil.toNumber('0.2')).toEqual(0.2);
        expect(StringUtil.toNumber('03')).toEqual(3);
        expect(StringUtil.toNumber('03,,2 %*()1çÇ~')).toEqual(3.21);
        expect(StringUtil.toNumber('10.')).toEqual(10);
        expect(StringUtil.toNumber('1.91')).toEqual(1.91);         
        expect(StringUtil.toNumber('.911')).toEqual(0.91);         
        expect(StringUtil.toNumber('30.000')).toEqual(30000); 
        expect(StringUtil.toNumber('30.00')).toEqual(30);
        expect(StringUtil.toNumber('300.000.000.00')).toEqual(300000000);
        expect(StringUtil.toNumber('300.000.000,00')).toEqual(300000000);
        expect(StringUtil.toNumber('300.000.000,00000')).toEqual(300000000);
        expect(StringUtil.toNumber('300.000.000.000')).toEqual(300000000000);
        expect(StringUtil.toNumber('300')).toEqual(300);
        expect(StringUtil.toNumber('99')).toEqual(99);
        expect(StringUtil.toNumber('9')).toEqual(9);
        expect(StringUtil.toNumber('01.99')).toEqual(1.99);
        expect(StringUtil.toNumber('01,99')).toEqual(1.99);
        expect(StringUtil.toNumber('01,999')).toEqual(1999);
        expect(StringUtil.toNumber('40000')).toEqual(40000);
        expect(StringUtil.toNumber('10000')).toEqual(10000);
        expect(StringUtil.toNumber('500')).toEqual(500);
        expect(StringUtil.toNumber('30000')).toEqual(30000);
        expect(StringUtil.toNumber('0')).toEqual(0);
        expect(StringUtil.toNumber('15000')).toEqual(15000);
        expect(StringUtil.toNumber('100')).toEqual(100);
        expect(StringUtil.toNumber('8500')).toEqual(8500);
        expect(StringUtil.toNumber('2600')).toEqual(2600);
        expect(StringUtil.toNumber('56468')).toEqual(56468);
        expect(StringUtil.toNumber('12705.3')).toEqual(12705.3);
    }); 

    it('must return zero when invalid numbers ', () => {                
        expect(StringUtil.toNumber('R$')).toEqual(0);                                                         
        expect(StringUtil.toNumber('.')).toEqual(0);    
        expect(StringUtil.toNumber('SIM')).toEqual(0);  
        expect(StringUtil.toNumber(' NAO ')).toEqual(0);  
        expect(StringUtil.toNumber('000.SIM,000')).toEqual(0);   
        expect(StringUtil.toNumber(',')).toEqual(0);
        expect(StringUtil.toNumber(',...')).toEqual(0);
    });
    
});