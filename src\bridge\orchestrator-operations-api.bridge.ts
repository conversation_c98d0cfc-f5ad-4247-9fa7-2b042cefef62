import { HttpService } from '@nestjs/axios';
import { Injectable, Logger } from '@nestjs/common';
import { Context } from '../config/context/model/context';
import { ContextService } from '../config/context/service/context.service';
import { EndpointConfig } from '../config/endpoint/endpoint.config';

@Injectable()
export class OrchestratorOperationsApiBridge {
  private readonly logger: Logger;

  constructor(
    private readonly endpointConfig: EndpointConfig,
    private readonly httpService: HttpService,
    private readonly contextService: ContextService,
  ) {
    this.logger = new Logger(OrchestratorOperationsApiBridge.name);
  }

  public getContext(): Context {
    return this.contextService.current();
  }

  async getAvails(params: any): Promise<any> {
    const uri = `${this.endpointConfig.getOrchestratorOperationsUrl()}/v1/avail`;

    //default pageSize = 100
    params.pageSize = params?.pageSize || 100;

    this.logger.log(`Executing http get request at ${uri}`);

    // Log the curl command for this request
    this.printCurlCommand(
      uri,
      this.getContext().getCommomRequestHeadersToCorpSvaInsurance(),
      params,
    );

    const { data } = await this.httpService.axiosRef.get<any>(uri, {
      headers: this.getContext().getCommomRequestHeadersToCorpSvaInsurance(),
      params: {
        ...params,
        sortOrder: 'RANKING',
      },
    });

    return data;
  }

  async getAvailDetails(id: any, params: any): Promise<any> {
    const uri = `${this.endpointConfig.getOrchestratorOperationsUrl()}/v1/avail/detail/${id}`;

    this.logger.log(`Executing http get request at ${uri}`);

    this.printCurlCommand(
      uri,
      this.getContext().getCommomRequestHeadersToCorpSvaInsurance(),
      params,
    );

    const { data } = await this.httpService.axiosRef.get<any>(uri, {
      headers: this.getContext().getCommomRequestHeadersToCorpSvaInsurance(),
      params: params,
    });

    return data;
  }

  async getAvailById(params: any): Promise<any> {
    const uri = `${this.endpointConfig.getOrchestratorOperationsUrl()}/v1/avail/${params}`;

    this.logger.log(`Executing http get request at ${uri}`);

    this.printCurlCommand(
      uri,
      this.getContext().getCommomRequestHeadersToCorpSvaInsurance(),
    );

    const { data } = await this.httpService.axiosRef.get<any>(uri, {
      headers: this.getContext().getCommomRequestHeadersToCorpSvaInsurance(),
    });
    return data;
  }

  // Método genérico para montar e printar o comando curl sem o --data em GET
  private printCurlCommand(url: string, headers: any, params?: any) {
    try {
      // Adicionar parâmetros de query à URL se existirem
      if (params) {
        const paramString = new URLSearchParams(params).toString();
        url += `?${paramString}`;
      }

      // Iniciar o comando curl com a URL
      let curl = `curl -X GET '${url}'`;

      // Adicionar headers ao comando curl
      Object.keys(headers).forEach((key) => {
        curl += ` -H '${key}: ${headers[key]}'`;
      });

      // Log formatado com uma aparência mais elegante e sem body para GET
      this.logger.log(`
================== CURL GATEWAY ORCHESTRATOR ==================
${curl}
=============================
`);
    } catch (error) {
      // Caso ocorra um erro, logamos o erro, mas não interrompemos a execução
      this.logger.log('Error generating CURL command', error);
    }
  }
}
