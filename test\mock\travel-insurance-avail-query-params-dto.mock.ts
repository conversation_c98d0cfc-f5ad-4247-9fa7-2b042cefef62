import { TravelInsuranceAvailQueryParamsDTO } from "src/model/travel-insurance/request/travel-insurance-avail-query-params.request";
import { DateUtil } from "src/util/date.util";

export class TravelInsuranceAvailQueryParamsDTOMock {

    private constructor() {}

    static getTravelInsuranceAvailQueryParamsDTO(): TravelInsuranceAvailQueryParamsDTO {

        const queryParams = new TravelInsuranceAvailQueryParamsDTO();
        const date = new Date();
        const startDate = DateUtil.plusDays(date, 1).toISOString();
        const endDate = DateUtil.plusDays(date, 5).toISOString()

        queryParams.startDate = startDate;
        queryParams.endDate = endDate;
        queryParams.zoneId = 7110;
        queryParams.paxs = '30,30';

        return queryParams;
    }

}