import { Injectable } from '@nestjs/common';
import { isNumber } from 'class-validator';

@Injectable()
export class RankingConfig {
  private customRanking: Map<number, number>;
  private defaultRanking: number;

  constructor() {
    this.defaultRanking =
      process.env.BENEFITS_RANKING_DEFAULT == 'null'
        ? 100
        : Number(process.env.BENEFITS_RANKING_DEFAULT);

    const benefitsRankingCustom: string =
      process.env.BENEFITS_RANKING_CUSTOM == 'null'
        ? undefined
        : process.env.BENEFITS_RANKING_CUSTOM;

    this.customRanking = new Map<number, number>();

    if (benefitsRankingCustom) {
      const rankings: { benefitId: number; ranking: number }[] = [
        ...JSON.parse(benefitsRankingCustom),
      ];

      this.customRanking = new Map(
        rankings
          .filter(
            (r) =>
              r.benefitId &&
              isNumber(Number(r.benefitId)) &&
              isNumber(Number(r.ranking)),
          )
          .map((r) => [Number(r.benefitId), Number(r.ranking)]),
      );
    }
  }

  getDefaultRanking(): number {
    return this.defaultRanking;
  }

  getCustomRanking(): Map<number, number> {
    return this.customRanking;
  }
}
