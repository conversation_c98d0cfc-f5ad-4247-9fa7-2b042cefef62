apiVersion: v1
kind: ConfigMap
metadata:
  name: bff-typescript-express-web-travel-insurance
  namespace: corp-sva
data:
  ADDITIONAL_OPTS: " "
  PREFIX: bff-typescript-express-web-travel-insurance
  VAULT_HOST: vault-dev.services.cvc.com.br
  VAULT_SCHEME: http
  CONSUL_HOST: consul-dev.services.cvc.com.br
  CONSUL_PORT: "8500"
  CONSUL_PATH: corp-sva/bff-typescript-express-web-travel-insurance
  NODE_ENV: ti