# BFF Seguro viagem

## SWAGGER LOCAL

<http://localhost:3000/swagger-ui/>

# bff-typescript-express-web-travel-insurance

- Usa a versão 16 do node.
- Precisa do npm versão 8, porém roda na 6.
- npm run build
- npm run start:debug

Exemplo de chamada para o endpoint de avail:

```sh
curl --location 'http://localhost:3000/insurances/store/avail?paxs=30%2C30%2C30&startDate=2024-12-23&endDate=2024-12-26&zoneId=7110&productTypes=INSURANCE&providerType=ASSIST_CARD' \
--header 'gtw-sec-user-token: eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************.kHPebKM-fc7W06I7jrCTZaivTIUCmclldRwuB4fYeFeyoUndV1ANVgOdM98ubMPs4Nyt6zdZeugyRE8yv0k0gD8GQzxLM6-muxlh8BUG5EJD2AUQ4-dPN6maOzBSW_mkodcaQTNCjC_gR-glBuk5b7KZkbkZRs22VIBEIeYGz5u0D0fyY0_tY7h65pPaDCnL4EqFImhu_zaRan1U9ksefAYUqGxDd9QhA8ZHKuukaHlgpkjp1todQCT5VCXtN06bUV-H87S85x15YRu10XRRSV2JAbufvWSDRYJOLZEf3fD26SGM8C5cLUS1KHY0que87zaSWQhmz5CeNp_C-b_bow' \
--header 'gtw-transaction-id: 085bc02f-e6fc-403b-963e-1f00ca88f1c1' \
--header 'Content-Type: application/json' \
--data '{
    "selectedItems": []
}'
```

[![Build Status](http://corp-jenkins01.compute.br-sao-1.cvccorp.cloud:8080/buildStatus/icon?job=MS%2Fbff-typescript-express-web-travel-insurance)](http://corp-jenkins01.compute.br-sao-1.cvccorp.cloud:8080/job/MS/job/bff-typescript-express-web-travel-insurance/)
[![Quality Gate](https://sonar.app.cvc.com.br/api/project_badges/measure?project=bff-typescript-express-web-travel-insurance&metric=alert_status)](https://sonar.app.cvc.com.br/dashboard?id=bff-typescript-express-web-travel-insurance)
[![Coverage](https://sonar.app.cvc.com.br/api/project_badges/measure?project=bff-typescript-express-web-travel-insurance&metric=coverage)](https://sonar.app.cvc.com.br/component_measures?id=bff-typescript-express-web-travel-insurance&metric=Coverage)
[![Maintainnability](https://sonar.app.cvc.com.br/api/project_badges/measure?project=bff-typescript-express-web-travel-insurance&metric=sqale_rating)](https://sonar.app.cvc.com.br/component_measures?id=bff-typescript-express-web-travel-insurance&metric=Maintainability)
[![Security](https://sonar.app.cvc.com.br/api/project_badges/measure?project=bff-typescript-express-web-travel-insurance&metric=security_rating)](https://sonar.app.cvc.com.br/component_measures?id=bff-typescript-express-web-travel-insurance&metric=Security)

## Query para logs no Kibana

<https://kibana.services.cvc.com.br/app/kibana#/discover?_g=()&_a=(columns:!(message),index:'5725b180-ba9d-11e8-be0f-396272e87c50',interval:auto,query:(language:kuery,query:'kubernetes.namespace_name:%20%22bff-typescript-express-web-travel-insurance%22%20'),sort:!('@timestamp',desc))>

# bff-typescript-express-web-travel-insurance

Boilerplate para criar aplicações usando Typescript e o Express, com uma estrutura básica pronta para a infraestrutura da CVC CORP.

## PRINCIPAIS DEPENDÊNCIAS

- [Express](https://expressjs.com/)

## INSTALAÇÃO E EXECUÇÃO

Instalação de dependências:

```sh
npm ci
```

### Criação de variáveis de ambiente

Para utilizar variáveis de ambientes no projeto basta criar um arquivo `.env` seguindo o exemplo do `.env.local`

### Executar a aplicação local

Comandos necessários para executar a aplicação:

```sh
npm run start:local
```

### Executar a aplicação via docker

Comandos necessários para executar a aplicação no container docker:

```sh
docker build -f Dockerfile .
docker run <ID>
```

### Execução de testes

Os comandos para execução dos testes podem ser chamados manualmente através do comando:

```sh
npm run test
```

Ou o mesmo é executado através do Git hook ao commitar uma alteração.
<br />

## INFRAESTRUTURA

### Ambientes

- **Desenvolvimento**: [https://bff-typescript-express-web-travel-insurance.k8s-ti-cvc.com.br/health](https://bff-typescript-express-web-travel-insurance.k8s-ti-cvc.com.br/health)
- **Homologação**: [https://bff-typescript-express-web-travel-insurance.k8s-qa-cvc.com.br/health](https://bff-typescript-express-web-travel-insurance.k8s-qa-cvc.com.br/health)
- **Produção**: [https://bff-typescript-express-web-travel-insurance.k8s-cvc.com.br/health](https://bff-typescript-express-web-travel-insurance.k8s-cvc.com.br/health)

### Documentação

- **Desenvolvimento**: [https://bff-typescript-express-web-travel-insurance.k8s-ti-cvc.com.br/redoc](https://bff-typescript-express-web-travel-insurance.k8s-ti-cvc.com.br/redoc)
- **Homologação**: [https://bff-typescript-express-web-travel-insurance.k8s-qa-cvc.com.br/redoc](https://bff-typescript-express-web-travel-insurance.k8s-qa-cvc.com.br/redoc)
- **Produção**: [https://bff-typescript-express-web-travel-insurance.k8s-cvc.com.br/redoc](https://bff-typescript-express-web-travel-insurance.k8s-cvc.com.br/redoc)

### Sonar

- [https://sonar.app.cvc.com.br/dashboard?id=bff-typescript-express-web-travel-insurance](https://sonar.app.cvc.com.br/dashboard?id=bff-typescript-express-web-travel-insurance)

### Jenkins

- [http://corp-jenkins01.compute.br-sao-1.cvccorp.cloud:8080/job/MS/job/bff-typescript-express-web-travel-insurance](http://corp-jenkins01.compute.br-sao-1.cvccorp.cloud:8080/job/MS/job/bff-typescript-express-web-travel-insurance)

### Vault

- **Desenvolvimento**: [http://vault-dev.services.cvc.com.br:8200/ui/vault/auth?with=token](http://vault-dev.services.cvc.com.br:8200/ui/vault/auth?with=token)
- **Homologação**: [http://vault-qa.services.cvc.com.br:8200/ui/vault/auth?with=token](http://vault-qa.services.cvc.com.br:8200/ui/vault/auth?with=token)
- **Produção**: [http://vault-prod.services.cvc.com.br:8200/ui/vault/auth?with=token](http://vault-prod.services.cvc.com.br:8200/ui/vault/auth?with=token)

### Consul

- **Desenvolvimento**: [http://consul-dev.services.cvc.com.br:8500/ui/](http://consul-dev.services.cvc.com.br:8500/ui/)
- **Homologação**: [http://consul-qa.services.cvc.com.br:8500/ui/](http://consul-qa.services.cvc.com.br:8500/ui/)
- **Produção**: [http://consul-prod.services.cvc.com.br:8500/ui/](http://consul-prod.services.cvc.com.br:8500/ui/)

## CRIAÇÃO DE CHAMADOS

- [Time de DevOps](https://cvccorp.atlassian.net/wiki/spaces/devops/pages/9342418983/Abertura+de+chamados+incidentes+-+DevOps)
- [Time de Cloud](https://cvccorp.atlassian.net/servicedesk/customer/portal/9/group/221/create/1351)

## ARQUITETURA

- [O que não fazemos?](https://cvccorp.atlassian.net/wiki/spaces/AR/pages/9336160362/O+Que+N+o+Fazemos)
- [Wiki de Soluções](https://cvccorp.atlassian.net/wiki/spaces/AR/pages/9199781127/Wiki+de+Solu+es)
- [Componentes de Plataforma.](https://cvccorp.atlassian.net/wiki/spaces/AR/pages/9199845673/Componentes+de+Plataforma)
