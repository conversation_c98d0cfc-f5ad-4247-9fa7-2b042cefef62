apiVersion: apps/v1
kind: Deployment
metadata:
  name: bff-typescript-express-web-travel-insurance-deploy
  namespace: corp-sva
  labels:
    app: bff-typescript-express-web-travel-insurance
spec:
  replicas: 1
  selector:
    matchLabels:
      app: bff-typescript-express-web-travel-insurance
  template:
    metadata:
      labels:
        app: bff-typescript-express-web-travel-insurance
      annotations:
        vault.security.banzaicloud.io/vault-addr: __VAULT_ADDR__
    spec:
      serviceAccountName: bff-typescript-express-web-travel-insurance
      containers:
      - name: bff-typescript-express-web-travel-insurance
        image: ************.dkr.ecr.sa-east-1.amazonaws.com/bff-typescript-express-web-travel-insurance:__TAG__
        imagePullPolicy: Always
        resources:
          requests:
            memory: "256Mi"
            cpu: "80m"
          limits:
            memory: "800Mi"
            cpu: "500m"
        readinessProbe:
          failureThreshold: 3
          httpGet:
            path: /health
            port: 8080
            httpHeaders:
            - name: X-Custom-Header
              value: ReadinessProbe
          initialDelaySeconds: 30
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 10
        livenessProbe:
          failureThreshold: 3
          httpGet:
            path: /health
            port: 8080
            httpHeaders:
            - name: X-Custom-Header
              value: LivenessProbe
          initialDelaySeconds: 35
          periodSeconds: 15
          successThreshold: 1
          timeoutSeconds: 10
        envFrom:
          - configMapRef:
              name: bff-typescript-express-web-travel-insurance
          #- secretRef:
          #    name: bff-typescript-express-web-travel-insurance
        ports:
        - containerPort: 8080
        - containerPort: 5005
