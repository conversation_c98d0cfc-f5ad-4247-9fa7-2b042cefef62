import { Controller, Get } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
@ApiTags('Health')
@Controller('health')
export class HealthController {
  constructor() {}

  @Get()
  check() {
    return {
      status: 'ok',
      info: {
        'corp-sva-insurance': {
          status: 'up',
        },
        'api-springboot-travel-voucher (used to search country code by zone id)':
          {
            status: 'up',
          },
      },
      error: {},
      details: {
        'corp-sva-insurance': {
          status: 'up',
        },
        'api-springboot-travel-voucher (used to search country code by zone id)':
          {
            status: 'up',
          },
      },
    };
  }
}
