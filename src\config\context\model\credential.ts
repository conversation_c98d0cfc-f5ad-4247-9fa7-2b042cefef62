export class Credential {
  private _personId: number;
  private _userId: number;
  private _name: string;
  private _email: string;
  private _cpf: string;
  private _branchId: number;
  private _agentSign: string;
  private _user: string;
  private _userType: string;

  constructor(
    personId: number,
    userId: number,
    name: string,
    email: string,
    cpf: string,
    branchId: number,
    agentSign: string,
    user: string,
    userType: string,
  ) {
    this._personId = personId;
    this._userId = userId;
    this._name = name;
    this._email = email;
    this._cpf = cpf;
    this._branchId = branchId;
    this._agentSign = agentSign;
    this._user = user;
    this._userType = userType;
  }

  static hydrate(credential: Credential) {
    return new Credential(
      credential.personId,
      credential.userId,
      credential.name,
      credential.email,
      credential.cpf,
      credential.branchId,
      credential.agentSign,
      credential.user,
      credential.userType,
    );
  }

  public get personId() {
    return this._personId;
  }

  public get userId() {
    return this._userId;
  }

  public get name() {
    return this._name;
  }

  public get email() {
    return this._email;
  }

  public get cpf() {
    return this._cpf;
  }

  public get branchId() {
    return this._branchId;
  }

  public get agentSign() {
    return this._agentSign;
  }

  public get user() {
    return this._user;
  }

  public get userType() {
    return this._userType;
  }
}
