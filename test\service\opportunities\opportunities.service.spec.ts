import { HttpService } from '@nestjs/axios';
import { HttpException, HttpStatus } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { OpportunitiesApiBridge } from 'src/bridge/opportunities-api.bridge';
import { Context } from 'src/config/context/model/context';
import { ContextService } from 'src/config/context/service/context.service';
import { EndpointConfig } from 'src/config/endpoint/endpoint.config';
import { OpportunitiesConfig } from 'src/config/opportunities/opportunities.config';
import { MetricCalculate } from 'src/model/metric/metric-calculate';
import { OpportunitiesService } from 'src/service/opportunities/opportunities.service';
import { ContextMock } from '../../mock/context.mock';
import { OpportunitiesMock } from '../../../test/mock/opportunities.mock';
import { TravelInsuranceMock } from '../../../test/mock/travel-insurance.mock';

describe('OpportunitiesService', () => {
    let service: OpportunitiesService;
    let bridge: OpportunitiesApiBridge; 
    let config: OpportunitiesConfig;        

    const endpointConfigMock = {
        getOpportunitiesUrl(): string {
            return 'https://test/opportunities';
        }
    } 

    const contextServiceMock = {
        current(): Context {
            return ContextMock.getContext();
        }
    }
    
    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [
                OpportunitiesService,                                
                OpportunitiesApiBridge,
                OpportunitiesConfig,
                {
                    provide: EndpointConfig,
                    useValue: endpointConfigMock
                },
                {
                    provide: HttpService,
                    useValue: jest.mock('axios')
                },
                {
                    provide: ContextService,
                    useValue: contextServiceMock
                }
            ]                                                  
        }).compile();

        service = await module.resolve(OpportunitiesService);               
        config = await module.resolve(OpportunitiesConfig);     
        bridge = module.get<OpportunitiesApiBridge>(OpportunitiesApiBridge);       
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('should be defined', () => {           
        expect(service).toBeDefined();        
        expect(bridge).toBeDefined();
        expect(config).toBeDefined();
    });

    it('must apply opportunities and return travel insurances', () => {

        const travelInsurances = TravelInsuranceMock.getTravelInsurances();
        const selectedItems = OpportunitiesMock.getOpportunitiesItemRequest();
        const opportunitiesResponse = OpportunitiesMock.getOpportunitiesResponse();
        const metric = MetricCalculate.newInstance();
        
        const configCalled = jest.spyOn(config, 'isItToLookForOpportunities')
            .mockImplementation(() => true);

        const bridgeCalled = jest.spyOn(bridge, 'hasOpportunities')
            .mockImplementation(() => Promise.resolve(opportunitiesResponse));        

        service.applyOpportunitiesIfAnyAndReturnTravelInsurances(travelInsurances, selectedItems, metric)
            .then(() => {
                expect(travelInsurances).toBeTruthy();  
                expect(travelInsurances[0].promotion).toBeTruthy();
                expect(travelInsurances[0].promotion).toEqual(opportunitiesResponse.availableItems[0].promotion);
                expect(configCalled).toHaveBeenCalledTimes(1);
                expect(bridgeCalled).toHaveBeenCalledTimes(1);                                                            
            });            
    });
    
    it('must apply opportunities and return travel insurance', () => {

        const travelInsurance = TravelInsuranceMock.getTravelInsurances()[0];
        const selectedItems = [];
        const opportunitiesResponse = OpportunitiesMock.getOpportunitiesResponseWithOneAvailableItem();
        const metric = MetricCalculate.newInstance();
        
        const configCalled = jest.spyOn(config, 'isItToLookForOpportunities')
            .mockImplementation(() => true);

        const bridgeCalled = jest.spyOn(bridge, 'hasOpportunities')
            .mockImplementation(() => Promise.resolve(opportunitiesResponse));        

        service.applyOpportunitiesIfAnyAndReturnTravelInsurance(travelInsurance, selectedItems, metric)
            .then(() => {                                        
                expect(travelInsurance).toBeTruthy();  
                expect(travelInsurance.promotion).toBeTruthy();
                expect(travelInsurance.promotion).toEqual(opportunitiesResponse.availableItems[0].promotion);
                expect(configCalled).toHaveBeenCalledTimes(1);
                expect(bridgeCalled).toHaveBeenCalledTimes(1);                                                            
            });            
    });

    it('must not apply opportunities when isItToLookForOpportunities is false', () => {

        const travelInsurances = TravelInsuranceMock.getTravelInsurances();
        const selectedItems = OpportunitiesMock.getOpportunitiesItemRequest();    
        const metric = MetricCalculate.newInstance();    
        
        const configCalled = jest.spyOn(config, 'isItToLookForOpportunities')
            .mockImplementation(() => false);

        const bridgeCalled = jest.spyOn(bridge, 'hasOpportunities')
            .mockImplementation(() => Promise.resolve(undefined));

        service.applyOpportunitiesIfAnyAndReturnTravelInsurances(travelInsurances, selectedItems, metric)
            .then(() => {
                expect(travelInsurances).toBeTruthy();                  
                expect(travelInsurances[0].promotion).toBeUndefined();
                expect(configCalled).toHaveBeenCalledTimes(1);
                expect(bridgeCalled).toHaveBeenCalledTimes(0);                                                            
            });            
    });

    it('must return only travel insurances when opportunities not exists', () => {

        const travelInsurances = TravelInsuranceMock.getTravelInsurances();
        const selectedItems = [];
        const metric = MetricCalculate.newInstance();
        
        const configCalled = jest.spyOn(config, 'isItToLookForOpportunities')
            .mockImplementation(() => true);

        const bridgeCalled = jest.spyOn(bridge, 'hasOpportunities')
            .mockImplementation(() => Promise.resolve(undefined));        

        service.applyOpportunitiesIfAnyAndReturnTravelInsurances(travelInsurances, selectedItems, metric)
            .then(() => {
                expect(travelInsurances).toBeTruthy();  
                expect(travelInsurances[0].promotion).toBeUndefined();                                
                expect(configCalled).toHaveBeenCalledTimes(1);
                expect(bridgeCalled).toHaveBeenCalledTimes(1);                                                            
            });            
    });

    it('must return only travel insurances when opportunities meta returned with error', () => {

        const travelInsurances = TravelInsuranceMock.getTravelInsurances();
        const selectedItems = OpportunitiesMock.getOpportunitiesItemRequest();
        const opportunitiesResponse = OpportunitiesMock.getOpportunitiesResponseWithMetaWithError();
        const metric = MetricCalculate.newInstance();
        
        const configCalled = jest.spyOn(config, 'isItToLookForOpportunities')
            .mockImplementation(() => true);

        const bridgeCalled = jest.spyOn(bridge, 'hasOpportunities')
            .mockImplementation(() => Promise.resolve(opportunitiesResponse));        

        service.applyOpportunitiesIfAnyAndReturnTravelInsurances(travelInsurances, selectedItems, metric)
            .then(() => {
                expect(travelInsurances).toBeTruthy();  
                expect(travelInsurances[0].promotion).toBeUndefined();                                
                expect(configCalled).toHaveBeenCalledTimes(1);
                expect(bridgeCalled).toHaveBeenCalledTimes(1);                                                           
            });            
    });

    it('must return only travel insurances when opportunities not match', () => {

        const travelInsurances = TravelInsuranceMock.getTravelInsurances();
        const selectedItems = OpportunitiesMock.getOpportunitiesItemRequest();
        const opportunitiesResponse = OpportunitiesMock.getOpportunitiesResponseWithOneAvailableItem();
        const metric = MetricCalculate.newInstance();

        travelInsurances[0].id = 3;
        travelInsurances[1].id = 4;
        
        const configCalled = jest.spyOn(config, 'isItToLookForOpportunities')
            .mockImplementation(() => true);

        const bridgeCalled = jest.spyOn(bridge, 'hasOpportunities')
            .mockImplementation(() => Promise.resolve(opportunitiesResponse));        

        service.applyOpportunitiesIfAnyAndReturnTravelInsurances(travelInsurances, selectedItems, metric)
            .then(() => {
                expect(travelInsurances).toBeTruthy(); 
                expect(travelInsurances[0].promotion).toBeUndefined();
                expect(configCalled).toHaveBeenCalledTimes(1);
                expect(bridgeCalled).toHaveBeenCalledTimes(1);                                                            
            });            
    });

    it('must return only travel insurances when opportunities fail', () => {

        const travelInsurances = TravelInsuranceMock.getTravelInsurances();
        const selectedItems = OpportunitiesMock.getOpportunitiesItemRequest();
        const metric = MetricCalculate.newInstance();
        
        const configCalled = jest.spyOn(config, 'isItToLookForOpportunities')
            .mockImplementation(() => true);

        const bridgeCalled = jest.spyOn(bridge, 'hasOpportunities')
            .mockImplementation(() => { throw new Error('Error getting opportunities') });        

        service.applyOpportunitiesIfAnyAndReturnTravelInsurances(travelInsurances, selectedItems, metric)
            .then(() => {
                expect(travelInsurances).toBeTruthy();                 
                expect(travelInsurances[0].promotion).toBeUndefined();
                expect(configCalled).toHaveBeenCalledTimes(1);
                expect(bridgeCalled).toHaveBeenCalledTimes(1);                                                            
            });            
    });

    it('must return only travel insurances when opportunities fail with http error', () => {

        const travelInsurances = TravelInsuranceMock.getTravelInsurances();
        const selectedItems = OpportunitiesMock.getOpportunitiesItemRequest();
        const metric = MetricCalculate.newInstance();
        
        const configCalled = jest.spyOn(config, 'isItToLookForOpportunities')
            .mockImplementation(() => true);

        const bridgeCalled = jest.spyOn(bridge, 'hasOpportunities')
            .mockImplementation(() => { throw new HttpException(
                { message: 'Error getting opportunities', status: HttpStatus.NOT_FOUND }, HttpStatus.NOT_FOUND) });        

        service.applyOpportunitiesIfAnyAndReturnTravelInsurances(travelInsurances, selectedItems, metric)
            .then(() => {
                expect(travelInsurances).toBeTruthy();                 
                expect(travelInsurances[0].promotion).toBeUndefined();
                expect(configCalled).toHaveBeenCalledTimes(1);
                expect(bridgeCalled).toHaveBeenCalledTimes(1);                                                            
            });            
    });
    
});