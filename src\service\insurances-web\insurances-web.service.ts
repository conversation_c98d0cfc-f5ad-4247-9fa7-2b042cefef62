import {
  HttpStatus,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { LinksConfig } from 'src/config/logo/links.config';
import { RankingConfig } from 'src/config/ranking/ranking.config';
import { MetricCalculate } from 'src/model/metric/metric-calculate';
import { InsuranceRequestBody } from 'src/model/opportunities/request/insurance-request-body';
import { OpportunitiesItemRequest } from 'src/model/opportunities/request/opportunities-item.request';
import {
  Content,
  Product,
  ProductsAvailResponse,
} from 'src/model/products/response/products-avail.response';
import { Benefit } from 'src/model/travel-insurance/benefit';
import { Rate } from 'src/model/travel-insurance/rate';
import { GroupItemResponse } from 'src/model/travel-insurance/response/group-item.response';
import { GroupResponse } from 'src/model/travel-insurance/response/group.response';
import { LinkResponse } from 'src/model/travel-insurance/response/link.response';
import { MetaForHasavailResponse } from 'src/model/travel-insurance/response/meta-for-hasavail.response';
import { MetaResponse } from 'src/model/travel-insurance/response/meta.response';
import { TravelInsuranceAvailResponse } from 'src/model/travel-insurance/response/travel-insurance-avail.response';
import { TravelInsuranceHasavailResponse } from 'src/model/travel-insurance/response/travel-insurance-hasavail.response';
import { TravelInsurance } from 'src/model/travel-insurance/travel-insurance';
import { MetaUtil } from 'src/util/meta.util';
import { OrchestratorOperationsApiBridge } from '../../bridge/orchestrator-operations-api.bridge';
import { Insurance } from '../../model/travel-insurance/response/Insurance';
import { AvailResponse } from '../../model/travel-insurance/response/avail.response';
import { MetaAvailResponse } from '../../model/travel-insurance/response/meta.avail.response';
import { TravelInsuranceAvailDetailResponse } from '../../model/travel-insurance/response/travel-insurance-detail.response';
import { OpportunitiesService } from '../opportunities/opportunities.service';
import { RankingService } from '../ranking/ranking.service';

@Injectable()
export class InsurancesWebService {
  private logger: Logger;

  constructor(
    private readonly bridge: OrchestratorOperationsApiBridge,
    private readonly links: LinksConfig,
    private readonly config: RankingConfig,
    private readonly opportunitiesService: OpportunitiesService,
    private readonly rankingService: RankingService,
  ) {
    this.logger = new Logger(InsurancesWebService.name);
  }

  async getStoreDetails(id: any, query: any) {
    const metric = MetricCalculate.newInstance().start();
    const metricToPromotion = MetricCalculate.newInstance();
    const productsAvailResponse: Product = await this.bridge.getAvailDetails(
      id,
      query,
    );
    const passengers = this.getPassangers(query);

    const selectedItems = productsAvailResponse.prices.flatMap((price) =>
      price.rates.map((rate) => ({ rph: rate.rph, rateToken: rate.rateToken })),
    );

    let insurances = this.buildInsurances([productsAvailResponse], passengers);
    insurances = await this.applyOpportunities(
      insurances,
      selectedItems,
      metricToPromotion,
    );
    insurances = this.rankBenefits(insurances);
    const meta = this.buildMetaAvailWithoutMetrics(insurances);
    const response = insurances.map((ins) =>
      this.buildInsuranceReponse(
        ins,
        [productsAvailResponse].find((prd) => prd.id == ins.id.toString()),
      ),
    );
    return this.buildTravelInsuranceAvailDetailResponse(
      response,
      meta,
      metric,
      metricToPromotion,
    );
  }
  async getStoreAvails(params: any, requestBody: InsuranceRequestBody) {
    const selectedItems = requestBody.selectedItems;

    const metric = MetricCalculate.newInstance().start();
    const metricToPromotion = MetricCalculate.newInstance();
    const productsAvailResponse: ProductsAvailResponse =
      await this.bridge.getAvails(params);

    const passengers = this.getPassangers(params);

    let insurances = this.buildInsurances(
      productsAvailResponse.products,
      passengers,
    );
    insurances = await this.applyOpportunities(
      insurances,
      selectedItems,
      metricToPromotion,
    );
    insurances = this.rankBenefits(insurances);
    const meta = this.buildMetaAvailWithoutMetrics(insurances);

    const response = insurances.map((ins) => {
      return this.buildInsuranceReponse(
        ins,
        productsAvailResponse.products.find(
          (prd) => prd.agreement == ins.id.toString(),
        ),
      );
    });

    return this.buildAvailResponse(response, meta, metric, metricToPromotion);
  }

  private buildInsuranceReponse(
    insurance: TravelInsurance,
    productAvailResponse,
  ) {
    return {
      rateToken: insurance.insuranceToken,
      id: insurance.id,
      eventCode: insurance.agreement,
      startDate: insurance.startDate,
      endDate: insurance.endDate,
      passengers: insurance.passengers,
      name: insurance.name,
      description: insurance.description,
      company: insurance.company,
      price: insurance.rate.priceWithTax,
      rates: insurance.rate,
      currency: insurance.rate.currency,
      cancelation: insurance.cancelable,
      contents: productAvailResponse.contents,
      links: {
        thumbnailImage: {
          href:
            productAvailResponse.links.find(
              (link) => link.name === 'default_partner_image',
            )?.href || '',
        },
      },
      hasCombo: insurance.promotion
        ? insurance.promotion.hasCombo
          ? insurance.promotion.hasCombo
          : false
        : false,
      promotion: insurance.promotion,
      imageCompany: insurance.imageCompany,
      acceptableDocuments: insurance.acceptableDocuments,
    };
  }

  async getAvails(
    params: any,
    requestBody: InsuranceRequestBody,
  ): Promise<TravelInsuranceAvailResponse> {
    const selectedItems = requestBody.selectedItems;

    const metric = MetricCalculate.newInstance().start();
    const metricToPromotion = MetricCalculate.newInstance();
    const productsAvailResponse: ProductsAvailResponse =
      await this.bridge.getAvails(params);

    const passengers = this.getPassangers(params);

    let insurances = this.buildInsurances(
      productsAvailResponse.products,
      passengers,
    );
    insurances = await this.applyOpportunities(
      insurances,
      selectedItems,
      metricToPromotion,
    );
    insurances = this.rankBenefits(insurances);
    const meta = this.buildMetaWithoutMetrics(insurances);

    return this.buildTravelInsuranceAvailResponse(
      insurances,
      meta,
      metric,
      metricToPromotion,
    );
  }

  async getHasAvail(
    rateToken: string,
    requestBody: InsuranceRequestBody,
  ): Promise<TravelInsuranceHasavailResponse> {
    const metric = MetricCalculate.newInstance().start();
    return await this.getHasavailByApplyingOpportunities(
      rateToken,
      requestBody,
      metric,
    );
  }

  private async getHasavailByApplyingOpportunities(
    rateToken: string,
    requestBody: InsuranceRequestBody,
    metric: MetricCalculate,
  ): Promise<TravelInsuranceHasavailResponse> {
    try {
      let insurance: TravelInsurance = await this.bridge.getAvailById(
        rateToken,
      );

      const metricToPromotion = MetricCalculate.newInstance();

      insurance = await this.applyOpportunity(
        insurance,
        requestBody.selectedItems,
        metricToPromotion,
      );
      insurance = this.rankingService.sortBenefits(insurance);

      return this.buildTravelInsuranceHasavailResponse(
        insurance,
        metric,
        metricToPromotion,
      );
    } catch (error) {
      if (
        error.response?.status === HttpStatus.INTERNAL_SERVER_ERROR ||
        error.response?.status === HttpStatus.NOT_FOUND
      ) {
        this.logger.error(error);
        throw new NotFoundException();
      } else {
        throw error;
      }
    }
  }

  private fromTravelInsuranceWithMeta(
    insurance: any,
    meta: MetaForHasavailResponse,
  ): TravelInsuranceHasavailResponse {
    const hasavail = new TravelInsuranceHasavailResponse();

    hasavail.insuranceToken = insurance.rateToken;
    hasavail.startDate = insurance.startDate;
    hasavail.endDate = insurance.endDate;
    hasavail.name = insurance.title;
    hasavail.description = insurance.description;

    hasavail.rate = {
      currency: insurance.currency,
      pricePerPaxWithTax: insurance.pricePerPaxWithTax,
      pricePerPaxWithoutTax: insurance.pricePerPaxWithoutTax,
      priceWithTax: insurance.priceWithTax,
      priceWithoutTax: insurance.priceWithoutTax,
    };

    hasavail.taxes = insurance.taxes;
    hasavail.cancelable = !insurance?.cancellationPolicy[0]?.nonRefundable;
    hasavail.meta = meta;
    hasavail.acceptableDocuments = insurance.acceptableDocuments;

    return hasavail;
  }

  private buildTravelInsuranceHasavailResponse(
    insurance: TravelInsurance,
    metric: MetricCalculate,
    metricToPromotion: MetricCalculate,
  ): TravelInsuranceHasavailResponse {
    const meta = new MetaForHasavailResponse();

    meta.metrics = [
      MetaUtil.applyMetrics(metric.collect(), metricToPromotion.collect()),
    ];

    return this.fromTravelInsuranceWithMeta(insurance, meta);
  }

  private async applyOpportunity(
    insurance: TravelInsurance,
    selectedItems: OpportunitiesItemRequest[],
    metricToPromotion: MetricCalculate,
  ) {
    return await this.opportunitiesService.applyOpportunitiesIfAnyAndReturnTravelInsurance(
      insurance,
      selectedItems,
      metricToPromotion,
    );
  }

  private buildTravelInsuranceAvailResponse(
    insurances: any[],
    meta: MetaResponse,
    metric: MetricCalculate,
    metricToPromotion: MetricCalculate,
  ): TravelInsuranceAvailResponse {
    const response = new TravelInsuranceAvailResponse();

    meta.metrics = [
      MetaUtil.applyMetrics(metric.collect(), metricToPromotion.collect()),
    ];

    response.content = insurances;
    response.meta = meta;

    return response;
  }

  private rankBenefits(insurances: TravelInsurance[]): TravelInsurance[] {
    return insurances.map((insurance) =>
      this.rankingService.sortBenefits(insurance),
    );
  }

  private async applyOpportunities(
    insurances: TravelInsurance[],
    selectedItems: OpportunitiesItemRequest[],
    metricToPromotion: MetricCalculate,
  ) {
    if (insurances.length) {
      insurances =
        await this.opportunitiesService.applyOpportunitiesIfAnyAndReturnTravelInsurances(
          insurances,
          selectedItems,
          metricToPromotion,
        );
    }

    return this.insurancesApplyingValueToFilter(insurances);
  }

  private insurancesApplyingValueToFilter(insurances: TravelInsurance[]) {
    return insurances.map((insurance) => {
      insurance.valueToFilter = insurance.promotion
        ? insurance.promotion.priceWithTax
        : insurance.rate.priceWithTax;
      return insurance;
    });
  }

  private createGroup(
    insurances: TravelInsurance[],
    groupName: string,
    initialId: number,
    isOnlyCovid: boolean,
  ): GroupResponse {
    let insurancesFiltered = insurances;

    if (isOnlyCovid) {
      insurancesFiltered = insurancesFiltered.filter(
        (insurance) =>
          insurance.additionalDescription &&
          insurance.additionalDescription.toLowerCase().includes('covid-19') &&
          !insurance.additionalDescription.toLowerCase().includes('não'),
      );
    }

    if (insurancesFiltered.length == 0) {
      return undefined;
    }

    const groupItems: GroupItemResponse[] = [];
    const uniqueNames = [
      ...new Set(insurancesFiltered.map((insurance) => insurance.name)),
    ];

    const all = new GroupItemResponse();
    all.id = initialId;
    all.name = `Todos (${insurancesFiltered.length})`;

    uniqueNames.forEach((name) => {
      let insurancesFilteredByName = [];

      insurancesFilteredByName = insurancesFiltered.filter(
        (insurance) => insurance.name == name,
      );

      const groupItemResponse = new GroupItemResponse();
      const insuranceName = name.trim();

      groupItemResponse.id = ++initialId;
      groupItemResponse.name = `${insuranceName} (${insurancesFilteredByName.length})`;
      groupItemResponse.value = encodeURIComponent(insuranceName);

      groupItems.push(groupItemResponse);
    });

    const group = new GroupResponse();
    group.name = groupName;
    group.items = [all, ...groupItems];

    return group;
  }

  private createGroups(insurances: TravelInsurance[]): GroupResponse[] {
    let initialId = 1;
    const typesOfInsurances = this.createGroup(
      insurances,
      'Tipo de seguro viagem',
      initialId,
      false,
    );

    initialId = typesOfInsurances.items.length + 1;
    const insurancesForCovid = this.createGroup(
      insurances,
      'Seguro viagem Covid-19',
      initialId,
      true,
    );

    if (!insurancesForCovid) {
      return [typesOfInsurances];
    }

    return [typesOfInsurances, insurancesForCovid];
  }

  private findInsuranceWithHighstValue(insurances: TravelInsurance[]): number {
    if (insurances.length == 0) {
      return 0;
    }

    return insurances.reduce((prev, current) =>
      prev.valueToFilter > current.valueToFilter ? prev : current,
    ).valueToFilter;
  }

  private getDefaultLinks(): LinkResponse[] {
    const type = 'GET';

    const partner = new LinkResponse();
    partner.type = type;
    partner.rel = 'default_partner';
    partner.href = this.links.getUniversalAssistanceUrl();

    const image = new LinkResponse();
    image.type = type;
    image.rel = 'default_partner_image';
    image.href = this.links.getUniversalAssistanceLogoUrl();

    return [partner, image];
  }

  private buildMetaWithoutMetrics(insurances: TravelInsurance[]): MetaResponse {
    const hasInsurances: boolean = insurances.length > 0;

    const metaResponse = new MetaResponse();
    metaResponse.groups = hasInsurances ? this.createGroups(insurances) : [];
    metaResponse.highestValue = this.findInsuranceWithHighstValue(insurances);
    metaResponse.links = hasInsurances ? this.getDefaultLinks() : [];

    return metaResponse;
  }

  private buildMetaAvailWithoutMetrics(
    insurances: TravelInsurance[],
  ): MetaAvailResponse {
    const hasInsurances: boolean = insurances.length > 0;

    const metaResponse = new MetaAvailResponse();
    metaResponse.groups = hasInsurances ? this.createGroups(insurances) : [];
    metaResponse.highestValue = this.findInsuranceWithHighstValue(insurances);
    metaResponse.links = hasInsurances ? this.getDefaultLinks() : [];

    return metaResponse;
  }

  private getCancelable = (product: Product): boolean => {
    const { prices } = product;
    return !prices[0].cancellationPolicy[0].nonRefundable;
  };

  private getStartDate = (product: Product): string => {
    const { prices } = product;

    return prices[0].rates[0].startDate;
  };

  private getEndDate = (product: Product): string => {
    const { prices } = product;

    return prices[0].rates[0].endDate;
  };

  private findRanking(key: number): number {
    return this.config.getCustomRanking().get(key);
  }

  private applyRankingAndBenefitValue(benefit: Benefit) {
    const ranking = this.findRanking(benefit.id);

    benefit.ranking = ranking ? ranking : this.config.getDefaultRanking();

    return benefit;
  }

  private getBenefits(product: Product): Benefit[] {
    const benefits: Benefit[] = [];

    const contents: Content[] = product.contents.filter(
      (c) => c.name === 'Benefits',
    );

    contents.forEach((content: Content) => {
      const items = content.items !== undefined ? content.items : [];

      items.forEach((item) => {
        // if (item.amount) {

        const benefit: Benefit = new Benefit();

        benefit.name = item.name;
        benefit.id = parseInt(item.code);
        benefit.value = item.amount
          ? '$' + item?.amount?.toLocaleString()
          : '$0';
        benefit.minAge = item.min;
        benefit.maxAge = item.max;
        benefit.description = item.description;
        this.applyRankingAndBenefitValue(benefit);
        benefits.push(benefit);

        // }
      });
    });

    return benefits;
  }

  private getRate(product: Product): Rate {
    const { prices } = product;
    const price = prices[0];
    const { rates, currency } = price;

    const {
      pricePerPaxWithTax,
      pricePerPaxWithoutTax,
      priceWithTax,
      priceWithoutTax,
    } = rates[0];

    const rate = {
      pricePerPaxWithTax: pricePerPaxWithTax,
      pricePerPaxWithoutTax: pricePerPaxWithoutTax,
      priceWithTax: priceWithTax,
      priceWithoutTax: priceWithoutTax,
      currency: currency,
    };

    return rate;
  }

  private getToken(product: Product): string {
    const { prices } = product;
    const price = prices[0];
    const { rates } = price;
    const rate = rates[0];
    const { rateToken } = rate;

    return rateToken;
  }

  private getPassangers(params: any): number {
    const passengers = 0;

    try {
      if (params.paxs) {
        return params.paxs.split(',').length;
      }
    } catch (err) {}

    return passengers;
  }

  private getInternactional(product: Product): boolean {
    const { category } = product;

    if (category.name === 'International') {
      return true;
    }

    return false;
  }

  private buildInsurances(
    products: Product[],
    passengers: number,
  ): TravelInsurance[] {
    const travelInsurances: TravelInsurance[] = [];

    products.forEach((product: Product) => {
      const travelInsurance = new TravelInsurance();
      travelInsurance.id = parseInt(product.agreement); // parseInt(product.id);
      travelInsurance.insuranceToken = this.getToken(product);
      travelInsurance.benefits = this.getBenefits(product);
      travelInsurance.startDate = this.getStartDate(product);
      travelInsurance.endDate = this.getEndDate(product);
      travelInsurance.passengers = passengers;
      travelInsurance.name = product.name;
      travelInsurance.description = product.description;
      travelInsurance.additionalDescription = product.description;
      travelInsurance.company = product.company;
      travelInsurance.rate = this.getRate(product);
      travelInsurance.taxes = []; //todo
      travelInsurance.international = this.getInternactional(product);
      travelInsurance.cancelable = this.getCancelable(product);
      travelInsurance.agreement = product.agreement;
      travelInsurance.imageCompany = product.links.find(
        (link) => link.name === 'default_partner_image',
      ).href;
      travelInsurance.acceptableDocuments = product.acceptableDocuments;

      travelInsurances.push(travelInsurance);
    });

    return travelInsurances;
  }

  private buildTravelInsuranceAvailDetailResponse(
    insurance: any,
    meta: MetaAvailResponse,
    metric: MetricCalculate,
    metricToPromotion: MetricCalculate,
  ) {
    const response = new TravelInsuranceAvailDetailResponse();

    meta.metrics = MetaUtil.applyMetrics(
      metric.collect(),
      metricToPromotion.collect(),
    );

    response.insurance = insurance;
    response.meta = meta;

    return response;
  }

  private buildAvailResponse(
    insurances: any[],
    meta: MetaAvailResponse,
    metric: MetricCalculate,
    metricToPromotion: MetricCalculate,
  ) {
    const response = new AvailResponse();
    meta.metrics = MetaUtil.applyMetrics(
      metric.collect(),
      metricToPromotion.collect(),
    );
    meta.count = insurances.length;
    const insuranceList = [];
    insurances.map((insurance) => {
      const availInsurance = new Insurance();
      availInsurance.id = insurance.id;
      availInsurance.insuranceToken = insurance.insuranceToken;
      availInsurance.benefits = insurance.benefits;
      availInsurance.id = insurance.id;
      availInsurance.eventCode = insurance.eventCode;
      availInsurance.startDate = insurance.startDate;
      availInsurance.endDate = insurance.endDate;
      availInsurance.passengers = insurance.passengers;
      availInsurance.name = insurance.name;
      availInsurance.description = insurance.description;
      availInsurance.additionalDescription = insurance.description;
      availInsurance.company = insurance.company;
      availInsurance.rates = [insurance.rates];
      availInsurance.taxes = [];
      availInsurance.international = insurance.international;
      availInsurance.cancelation = insurance?.cancelation;
      availInsurance.currency = insurance.currency;
      availInsurance.rateToken = insurance.rateToken;
      availInsurance.price = insurance.price;
      availInsurance.contents = insurance.contents;
      availInsurance.promotion = insurance.promotion;
      availInsurance.imageCompany = insurance.imageCompany;
      availInsurance.acceptableDocuments = insurance.acceptableDocuments;

      insuranceList.push(availInsurance);
    });
    response.insurances = insuranceList;
    response.meta = meta;
    return response;
  }
}
