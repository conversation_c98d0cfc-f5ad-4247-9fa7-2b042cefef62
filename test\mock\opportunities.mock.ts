import { Promotion } from "src/model/opportunities/promotion/promotion";
import { OpportunitiesItemRequest } from "src/model/opportunities/request/opportunities-item.request";
import { OpportunitiesItemResponse } from "src/model/opportunities/response/opportunities-item.response";
import { OpportunitiesMetaResponse } from "src/model/opportunities/response/opportunities-meta.response";
import { OpportunitiesResponse } from "src/model/opportunities/response/opportunities.response";

export class OpportunitiesMock {

    static RATE_TOKEN = `PHJhdGVUb2tlbiBhZ3M9IldFQiIgYnJpPSIxMDAwIiBjdXI9IkJSTCIgZHRmPSIyMDI
            yLTEwLTE3IiBlY3Q9IkJSIiBlemk9IjEzNjAxIiBta3A9IjAuMzUiIG1raT0iMjkwNTIyIiBwa2c9Il
            NUQU5EQUxPTkUiIHB4cz0iMzAsMzAiIHBvdD0iMTMxLjQiIHB3dD0iMTMxLjQiIHByZD0iVFJJIiBwaW
            Q9IjYwNzM0NyIgc2R0PSIyMDIyLTEwLTI1IiBzb3Q9IjM3NS40MyIgc3d0PSIzNzUuNDMiIGR0aT0iMj
            AyMi0xMC0xNyIgcGxhPSIxOTA0ODMyOSIgcm9tPSI2MDczNDciIC8+`;

    private constructor() {}

    static getOpportunitiesItemRequest(): OpportunitiesItemRequest[] {

        const opportunitiesItemRequestOne: OpportunitiesItemRequest = new OpportunitiesItemRequest();
        opportunitiesItemRequestOne.rph = 1;
        opportunitiesItemRequestOne.rateToken = this.RATE_TOKEN;
                 
        const opportunitiesItemRequestTwo: OpportunitiesItemRequest = new OpportunitiesItemRequest();
        opportunitiesItemRequestTwo.rph = 2;
        opportunitiesItemRequestTwo.rateToken = this.RATE_TOKEN;

        return [opportunitiesItemRequestOne, opportunitiesItemRequestTwo];        
    }

    static getOpportunitiesItemResponse(): OpportunitiesItemResponse[] {

        const promotion: Promotion = new Promotion();
        promotion.statements = [];
        promotion.priceWithTax = 375.43;
        promotion.priceWithoutTax = 375.43;
        promotion.pricePerDayWithTax = 131.4;
        promotion.pricePerDayWithoutTax = 131.4;
        promotion.percentage = 0.1;
        promotion.discountApplied = '14.6';
        promotion.promoIds = [];
        promotion.hasCombo = true;

        const opportunitiesItemResponseOne: OpportunitiesItemResponse = new OpportunitiesItemResponse();
        opportunitiesItemResponseOne.rph = 1;
        opportunitiesItemResponseOne.rateToken = this.RATE_TOKEN;
        opportunitiesItemResponseOne.promotion = promotion;
        opportunitiesItemResponseOne.hasCombo = true;
                 
        const opportunitiesItemResponseTwo: OpportunitiesItemResponse = new OpportunitiesItemResponse();
        opportunitiesItemResponseTwo.rph = 2;
        opportunitiesItemResponseTwo.rateToken = this.RATE_TOKEN;
        opportunitiesItemResponseTwo.promotion = null;
        opportunitiesItemResponseTwo.hasCombo = false;

        return [opportunitiesItemResponseOne, opportunitiesItemResponseTwo];        
    }

    static getOpportunitiesResponse(): OpportunitiesResponse {

        const opportunitiesResponse: OpportunitiesResponse = new OpportunitiesResponse();
        opportunitiesResponse.selectedItems = OpportunitiesMock.getOpportunitiesItemResponse();
        opportunitiesResponse.availableItems = OpportunitiesMock.getOpportunitiesItemResponse();

        return opportunitiesResponse;
    }

    static getOpportunitiesResponseWithOneAvailableItem(): OpportunitiesResponse {

        const opportunitiesResponse: OpportunitiesResponse = new OpportunitiesResponse();
        opportunitiesResponse.selectedItems = [];
        opportunitiesResponse.availableItems = [OpportunitiesMock.getOpportunitiesItemResponse()[0]];

        return opportunitiesResponse;
    }

    static getOpportunitiesResponseWithMetaWithError(): OpportunitiesResponse {

        const meta = new OpportunitiesMetaResponse();
        meta.error = ['erro one', 'error two'];

        const opportunitiesResponse: OpportunitiesResponse = new OpportunitiesResponse();
        opportunitiesResponse.selectedItems = OpportunitiesMock.getOpportunitiesItemResponse();
        opportunitiesResponse.availableItems = OpportunitiesMock.getOpportunitiesItemResponse();
        opportunitiesResponse.meta = meta;

        return opportunitiesResponse;
    }

}