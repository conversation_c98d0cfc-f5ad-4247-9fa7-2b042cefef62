import {
  Controller,
  Get,
  Param,
  Query,
  UseFilters,
  UseInterceptors,
} from '@nestjs/common';
import { <PERSON>pi<PERSON>eader, ApiParam, ApiTags } from '@nestjs/swagger';
import { ErrorFilter } from '../../error/error.filter';
import { ContextInterceptor } from '../../config/context/interceptor/context-interceptor';
import { RequestHeaders } from '../../config/context/model/request-headers.enum';
import { AvailService } from '../../service/avail/avail.service';

@Controller('products')
@ApiTags('Products')
@UseFilters(new ErrorFilter())
@UseInterceptors(ContextInterceptor)
@ApiHeader({ name: RequestHeaders.SEC_USER_TOKEN, required: true })
@ApiHeader({ name: RequestHeaders.TRANSACTION_ID, required: true })
export class ProductsController {
  constructor(private availService: AvailService) {}

  @Get('/avail')
  async getAvails(@Query() query: any) {
    return this.availService.getAvails(query);
  }

  @Get('/avail/:id')
  @ApiParam({ name: 'id', description: 'Avail Identifier', type: 'string' })
  async getAvailById(@Param('id') id) {
    return this.availService.getAvailById(id);
  }

  @Get('/avail/detail/:id')
  @ApiParam({ name: 'id', description: 'Avail Identifier', type: 'string' })
  async getAvailDetail(@Param('id') id: any, @Query() query: any) {
    return this.availService.getAvailDetail(id, query);
  }
}
