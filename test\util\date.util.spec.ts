import { DateUtil } from 'src/util/date.util';

describe('DateUtil', () => {
    
    const january = 0;
    const november = 10;  
    const december = 11;

    it('must plus days ', () => {                  
        expect(DateUtil.plusDays(new Date(2022, november, 29), 1)).toEqual(new Date(2022, november, 30));
        expect(DateUtil.plusDays(new Date(2022, january, 1), 15)).toEqual(new Date(2022, january, 16));
        expect(DateUtil.plusDays(new Date(2022, december, 31), 1)).toEqual(new Date(2023, january, 1));              
    });

    it('must format date to pt-br ', () => {                  
        expect(DateUtil.formatToPtBr(new Date(2022, november, 29))).toEqual('29/11/2022');                
    });   
    
});