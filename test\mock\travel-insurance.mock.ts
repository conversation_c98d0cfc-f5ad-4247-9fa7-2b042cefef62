import { Benefit } from "src/model/travel-insurance/benefit";
import { Rate } from "src/model/travel-insurance/rate";
import { TravelInsurance } from "src/model/travel-insurance/travel-insurance";
import { Promotion } from "src/model/opportunities/promotion/promotion";
import { Statement } from "src/model/opportunities/promotion/statement";
import { PromoId } from "src/model/opportunities/promotion/promo-id";

export class TravelInsuranceMock {

    static RATE_TOKEN = `PHJhdGVUb2tlbiBhZ3M9IldFQiIgYnJpPSIxMDAwIiBjdXI9IkJSTCIgZHRmPSIyMDIyLTEyLTMwIiBlY3Q9
        IkJSIiBlemk9IjcxMTAiIG1rcD0iMSIgbWtpPSIwIiBwbGM9IkJSTCIgcGtnPSJTVEFOREFMT05FIiBweHM9IjMwLDEwIiBwd3Q9I
        jkyIiBwb3Q9IjkyIiBwcmQ9IlRSSSIgcGlkPSI1MDQ5NjY2IiBzZHQ9IjIwMjItMTItMTMiIHNvdD0iMjYyLjg2IiBzd3Q9IjI2Mi
        44NiIgZHRpPSIyMDIyLTEyLTI2IiBzY3Q9IkJSIiBzc3Q9IlNQIiBzemk9Ijk1ODMiIGNtaT0iMSIgcGxhPSIxOTA0ODMyOSIgcm9
        tPSI2MDczNDciLz4=`;

    private constructor() {}

    static getTravelInsurances(): TravelInsurance[] {
        
        const benefitOne = new Benefit();
        benefitOne.name = 'Despesas gerais';
        benefitOne.value = 'R$ 100.000,00';        
        benefitOne.id = 2699;
        benefitOne.minAge = 0;
        benefitOne.maxAge = 0;
        benefitOne.benefitValue = 100000;

        const benefitTwo = new Benefit();
        benefitTwo.name = 'Despesas com acidentes';
        benefitTwo.value = 'R$ 50.000,00';        
        benefitTwo.id = 3025;
        benefitTwo.minAge = 0;
        benefitTwo.maxAge = 0;
        benefitTwo.benefitValue = 0;

        const benefitThree = new Benefit();
        benefitThree.name = 'Despesas com medicamentos';
        benefitThree.value = 'R$ 70.000,00';        
        benefitThree.id = 1234;
        benefitThree.minAge = 0;
        benefitThree.maxAge = 0;
        benefitThree.benefitValue = 70000;

        const benefitFour = new Benefit();
        benefitFour.name = 'Despesas com Falecimento';
        benefitFour.value = 'R$ 30.000,00';        
        benefitFour.id = 7358;
        benefitFour.minAge = 0;
        benefitFour.maxAge = 0;
        benefitFour.benefitValue = 30000;

        const rateOne = new Rate();
        rateOne.currency = 'BRL';
        rateOne.priceWithTax = 100.0;
        rateOne.priceWithoutTax = 100.0;
        rateOne.pricePerPaxWithTax = 146;
        rateOne.pricePerPaxWithoutTax = 146;
        
        const travelInsuranceOne: TravelInsurance = new TravelInsurance();
        travelInsuranceOne.id = 1;    
        travelInsuranceOne.insuranceToken = this.RATE_TOKEN;
        travelInsuranceOne.benefits = [benefitOne, benefitTwo, benefitThree, benefitFour];
        travelInsuranceOne.startDate = '2022-10-17';
        travelInsuranceOne.endDate = '2022-10-17';
        travelInsuranceOne.passengers = 2;
        travelInsuranceOne.name = 'Seguro Nacional Top';
        travelInsuranceOne.description = 'Nacional';
        travelInsuranceOne.company = 'TRAVELACE';
        travelInsuranceOne.rate = rateOne;
        travelInsuranceOne.taxes = [];
        travelInsuranceOne.cancelable = true;     
        travelInsuranceOne.additionalDescription = 'Não inclui cobertura Covid-19';
        
        const rateTwo = new Rate();
        rateTwo.currency = 'BRL';
        rateTwo.priceWithTax = 417.14;
        rateTwo.priceWithoutTax = 417.14;
        rateTwo.pricePerPaxWithTax = 50;
        rateTwo.pricePerPaxWithoutTax = 50;

        const travelInsuranceTwo: TravelInsurance = new TravelInsurance();
        travelInsuranceTwo.id = 2;    
        travelInsuranceTwo.insuranceToken = this.RATE_TOKEN;
        travelInsuranceTwo.benefits = [benefitOne];
        travelInsuranceTwo.startDate = '2022-10-17';
        travelInsuranceTwo.endDate = '2022-10-17';
        travelInsuranceTwo.passengers = 2;
        travelInsuranceTwo.name = 'Seguro Internacional';
        travelInsuranceTwo.description = 'Nacional';
        travelInsuranceTwo.company = 'TRAVELACE';
        travelInsuranceTwo.rate = rateTwo;
        travelInsuranceTwo.taxes = [];
        travelInsuranceTwo.cancelable = true;   
        travelInsuranceOne.additionalDescription = 'Cobertura Covid-19';     

        return [travelInsuranceOne, travelInsuranceTwo];        
    }

    static getTravelInsurancesWithPromotion(): TravelInsurance[] {
        
        return this.getTravelInsurances().map(insurance => {
            
            const statement = new Statement();
            statement.code = '6bR_mUAWQE22VMxjk6Uwyw';
            statement.name = 'Promotion Test';
            statement.type = 'PROMOTION';
            statement.discount = 10;

            const promoId = new PromoId();
            promoId.id = '6bR_mUAWQE22VMxjk6Uwyw';

            const promotion = new Promotion();
            promotion.priceWithTax = insurance.rate.priceWithTax - 10;
            promotion.priceWithoutTax = insurance.rate.priceWithoutTax - 10;
            promotion.pricePerDayWithTax = insurance.rate.priceWithoutTax / 2;
            promotion.pricePerDayWithoutTax = insurance.rate.priceWithoutTax / 2;
            promotion.percentage = 10;
            promotion.discountApplied = "10";
            promotion.statements = [ statement ];
            promotion.promoIds = [ promoId ];
            promotion.hasCombo = false;

            insurance.promotion = promotion;

            return insurance;
        });       
    }

    static getTravelInsurancesToOrder(): TravelInsurance[] {

        const benefitOne = new Benefit();
        benefitOne.name = 'Despesas com medicamentos';
        benefitOne.value = 'R$ 100.000,00';        
        benefitOne.id = 2699;
        benefitOne.minAge = 0;
        benefitOne.maxAge = 0;
        benefitOne.benefitValue = 100000;

        const rateOne = new Rate();
        rateOne.currency = 'BRL';
        rateOne.priceWithTax = 200.0;
        rateOne.priceWithoutTax = 200.0;
        rateOne.pricePerPaxWithTax = 100.0;
        rateOne.pricePerPaxWithoutTax = 100.0;
        
        const travelInsuranceOne: TravelInsurance = new TravelInsurance();
        travelInsuranceOne.id = 3;    
        travelInsuranceOne.insuranceToken = this.RATE_TOKEN;
        travelInsuranceOne.benefits = [benefitOne];
        travelInsuranceOne.startDate = '2022-10-17';
        travelInsuranceOne.endDate = '2022-10-17';
        travelInsuranceOne.passengers = 2;
        travelInsuranceOne.name = 'Seguro Nacional CVC';
        travelInsuranceOne.description = 'Nacional';
        travelInsuranceOne.company = 'TRAVELACE';
        travelInsuranceOne.rate = rateOne;
        travelInsuranceOne.taxes = [];
        travelInsuranceOne.cancelable = true; 
        travelInsuranceOne.additionalDescription = '';

        return [...this.getTravelInsurances(), travelInsuranceOne];
    }
    
}