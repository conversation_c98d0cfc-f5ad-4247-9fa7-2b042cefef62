import { Injectable } from '@nestjs/common';
import { OrchestratorOperationsApiBridge } from '../../../bridge/orchestrator-operations-api.bridge';
import { TravelInsurance } from 'src/model/travel-insurance/travel-insurance';
import { OpportunitiesService } from '../../opportunities/opportunities.service';
import { MetricCalculate } from 'src/model/metric/metric-calculate';
import { OpportunitiesItemRequest } from 'src/model/opportunities/request/opportunities-item.request';
import { MetaUtil } from 'src/util/meta.util';
import { InsuranceStoreWrapperResponse } from 'src/model/insurance/store/has-avail/insurance-store-hasavail-wrapper.response';
import { AvailParams } from 'src/model/avail/request/avail-params.request';
import { InsuranceStoreResponse } from 'src/model/insurance/store/has-avail/insurance-store-hasavail-insurance.response';
import { InsuranceStoreMetaResponse } from 'src/model/insurance/store/has-avail/insurance-store-hasavail-meta.response';
import { Rate } from 'src/model/travel-insurance/rate';

@Injectable()
export class InsuranceStoreService {
  constructor(
    private readonly bridge: OrchestratorOperationsApiBridge,
    private readonly opportunitiesService: OpportunitiesService,
  ) {}

  async hasAvail(params: AvailParams): Promise<InsuranceStoreWrapperResponse> {
    const metric = MetricCalculate.newInstance().start();
    const response = await this.bridge.getAvailById(params.pathParam);

    const { rph, rateToken } = response;

    const metricToPromotion = MetricCalculate.newInstance();
    const opportunity = this.buildOpportunity(rph, rateToken);
    const travelInsurance = this.buildTravelInsurance(rph, rateToken);
    await this.opportunitiesService.applyOpportunitiesIfAnyAndReturnTravelInsurance(
      travelInsurance,
      [opportunity],
      metricToPromotion,
    );

    const insurance = new InsuranceStoreResponse();

    insurance.rateToken = travelInsurance.insuranceToken;
    insurance.eventCode = 0; //TODO: Verify whats information can be mapped here
    insurance.startDate = response.startDate;
    insurance.endDate = response.endDate;
    insurance.passengers = Math.round(
      response.priceWithTax / response.pricePerPaxWithTax,
    );
    insurance.name = response.title;
    insurance.description = response.description;
    (insurance.company = ''), //TODO: Verify whats information can be mapped here
      (insurance.price = response.priceWithTax);

    const rate = new Rate();
    rate.currency = response.currency;
    rate.priceWithTax = response.priceWithTax;
    rate.priceWithoutTax = response.priceWithoutTax;
    rate.pricePerPaxWithTax = response.pricePerPaxWithTax;
    rate.pricePerPaxWithoutTax = response.pricePerPaxWithoutTax;
    insurance.rates = [rate];

    insurance.currency = response.currency;

    const cancellationPolicy = response.cancellationPolicy.find(
      (value) => value.nonRefundable,
    );
    insurance.cancelation = cancellationPolicy
      ? cancellationPolicy.nonRefundable
      : false;
    insurance.links = null; //TODO: Verify whats information can be mapped here
    insurance.hasCombo = travelInsurance.promotion
      ? travelInsurance.promotion.hasCombo
        ? travelInsurance.promotion.hasCombo
        : false
      : false;
    insurance.acceptableDocuments = response.acceptableDocuments;

    const meta = new InsuranceStoreMetaResponse();
    meta.metrics = MetaUtil.applyMetrics(
      metric.collect(),
      metricToPromotion.collect(),
    );

    return this.buildResponse(insurance, meta);
  }

  private buildOpportunity(
    rph: number,
    rateToken: string,
  ): OpportunitiesItemRequest {
    const opportunity = new OpportunitiesItemRequest();
    opportunity.rph = rph;
    opportunity.rateToken = rateToken;

    return opportunity;
  }

  private buildTravelInsurance(
    rph: number,
    rateToken: string,
  ): TravelInsurance {
    const travelInsurance = new TravelInsurance();
    travelInsurance.id = rph;
    travelInsurance.insuranceToken = rateToken;

    return travelInsurance;
  }

  private buildResponse(
    insurance: InsuranceStoreResponse,
    meta: InsuranceStoreMetaResponse,
  ): InsuranceStoreWrapperResponse {
    const response = new InsuranceStoreWrapperResponse();
    response.insurance = insurance;
    response.meta = meta;

    return response;
  }
}
