import { HttpStatus } from '@nestjs/common';
import { MetricResultResponse } from '../travel-insurance/response/metric/metric-result.response';

export class MetricCalculate {
  private _start: number;
  private _status: number;
  private _error: string;
  private _metricResult: MetricResultResponse;
  private _hasError = false;
  private _finished = false;

  private constructor() {}

  public static newInstance(): MetricCalculate {
    return new MetricCalculate();
  }

  public start(): MetricCalculate {
    this._start = Date.now();
    return this;
  }

  public sendError(status: number, error: string): MetricCalculate {
    this._hasError = true;
    this._status = status;
    this._error = error;
    return this;
  }

  public end(): void {
    this._finished = true;

    if (!this._start) {
      return;
    }

    if (!this._hasError) {
      this._status = HttpStatus.OK;
    }

    const duration = Date.now() - this._start;

    this._metricResult = this.buildResult(duration);
  }

  public collect(): MetricResultResponse {
    if (!this._finished) {
      this.end();
    }

    return this._metricResult;
  }

  private buildResult(duration: number): MetricResultResponse {
    const result = new MetricResultResponse();
    result.duration = duration;
    result.status = this._status;
    result.errorResponse = this._error;
    return result;
  }
}
