{"env": {"node": true, "es2021": true}, "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:node/recommended", "plugin:prettier/recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 12, "sourceType": "module", "project": "./tsconfig.json"}, "plugins": ["@typescript-eslint", "prettier"], "rules": {"prettier/prettier": "error", "no-console": "off", "eqeqeq": "error", "curly": "error", "semi": ["error", "always"], "quotes": ["error", "single"], "@typescript-eslint/no-unused-vars": ["warn", {"argsIgnorePattern": "^_"}], "node/no-unsupported-features/es-syntax": ["error", {"ignores": ["modules"]}], "node/no-missing-import": "off", "node/no-unpublished-import": "off"}}