import { Injectable } from '@nestjs/common';

@Injectable()
export class CountryCodeTranslatorConfig {
  private countryCodeTranslator: Map<string, string>;

  constructor() {
    const countryCodes: string =
      process.env.COUNTRY_CODE_TRANSLATOR == 'null'
        ? undefined
        : process.env.COUNTRY_CODE_TRANSLATOR;

    this.countryCodeTranslator = new Map<string, string>();

    if (countryCodes) {
      const translator: { from: string; to: string }[] = [
        ...JSON.parse(countryCodes),
      ];

      this.countryCodeTranslator = new Map(
        translator.filter((c) => c.from && c.to).map((c) => [c.from, c.to]),
      );
    }
  }

  getTranslator(): Map<string, string> {
    return this.countryCodeTranslator;
  }
}
