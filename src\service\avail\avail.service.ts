import { Injectable } from '@nestjs/common';
import { OrchestratorOperationsApiBridge } from '../../bridge/orchestrator-operations-api.bridge';

@Injectable()
export class AvailService {
  constructor(private readonly bridge: OrchestratorOperationsApiBridge) {}

  async getAvails(query: any): Promise<any> {
    return await this.bridge.getAvails(query);
  }

  async getAvailById(id: any): Promise<any> {
    return await this.bridge.getAvailById(id);
  }

  async getAvailDetail(id: any, query: any): Promise<any> {
    return await this.bridge.getAvailDetails(id, query);
  }
}
