import {
  HttpStatus,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { InsuranceApiBridge } from 'src/bridge/insurance-api.bridge';
import { TravelVoucherApiBridge } from 'src/bridge/travel-voucher-api.bridge';
import { LinksConfig } from 'src/config/logo/links.config';
import { BusinessException } from 'src/error/business-exception';
import { MetricCalculate } from 'src/model/metric/metric-calculate';
import { InsuranceRequestBody } from 'src/model/opportunities/request/insurance-request-body';
import { OpportunitiesItemRequest } from 'src/model/opportunities/request/opportunities-item.request';
import { TravelInsuranceOrder } from 'src/model/travel-insurance/enums/TravelInsuranceOrder';
import { TravelInsuranceAvailQueryParamsDTO } from 'src/model/travel-insurance/request/travel-insurance-avail-query-params.request';
import { GroupItemResponse } from 'src/model/travel-insurance/response/group-item.response';
import { GroupResponse } from 'src/model/travel-insurance/response/group.response';
import { LinkResponse } from 'src/model/travel-insurance/response/link.response';
import { MetaForHasavailResponse } from 'src/model/travel-insurance/response/meta-for-hasavail.response';
import { MetaResponse } from 'src/model/travel-insurance/response/meta.response';
import { TravelInsuranceAvailResponse } from 'src/model/travel-insurance/response/travel-insurance-avail.response';
import { TravelInsuranceHasavailResponse } from 'src/model/travel-insurance/response/travel-insurance-hasavail.response';
import { TravelInsurance } from 'src/model/travel-insurance/travel-insurance';
import { DateUtil } from 'src/util/date.util';
import { MetaUtil } from 'src/util/meta.util';
import { OpportunitiesService } from '../opportunities/opportunities.service';
import { RankingService } from '../ranking/ranking.service';
import { CountryCodeTranslatorConfig } from 'src/config/country-code-translator/country-code-translator.config';

@Injectable()
export class TravelInsuranceService {
  private logger: Logger;

  constructor(
    private readonly countryBridge: TravelVoucherApiBridge,
    private readonly bridge: InsuranceApiBridge,
    private readonly opportunitiesService: OpportunitiesService,
    private readonly rankingService: RankingService,
    private readonly links: LinksConfig,
    private readonly countryCodeTranslator: CountryCodeTranslatorConfig,
  ) {
    this.logger = new Logger(TravelInsuranceService.name);
  }

  async avail(
    params: TravelInsuranceAvailQueryParamsDTO,
    requestBody: InsuranceRequestBody,
  ): Promise<TravelInsuranceAvailResponse> {
    const metric = MetricCalculate.newInstance().start();

    this.validateParams(params);

    params.country = await this.getCountryCode(params.zoneId);

    const metricToPromotion = MetricCalculate.newInstance();

    let insurances = await this.getAvailByApplyingOpportunities(
      params,
      requestBody,
      metricToPromotion,
    );

    const meta = this.buildMetaWithoutMetrics(insurances);

    if (params.isItToFilter()) {
      insurances = this.filterInsurancesByAcceptableParams(insurances, params);
    }

    if (params.isItToOrder()) {
      this.sortInsurances(insurances, params.orderBy);
    }

    insurances = this.rankBenefits(insurances);

    return this.buildTravelInsuranceAvailResponse(
      insurances,
      meta,
      metric,
      metricToPromotion,
    );
  }

  async hasavail(
    rateToken: string,
    requestBody: InsuranceRequestBody,
  ): Promise<TravelInsuranceHasavailResponse> {
    const metric = MetricCalculate.newInstance().start();

    return await this.getHasavailByApplyingOpportunities(
      rateToken,
      requestBody,
      metric,
    );
  }

  private validateParams(params: TravelInsuranceAvailQueryParamsDTO): void {
    this.validateDateParams(
      new Date(params.startDate),
      new Date(params.endDate),
    );

    if (params.minValueIsGreaterThanMaxValue()) {
      throw new BusinessException(
        'Valor inicial não pode ser maior que valor final',
      );
    }
  }

  private validateDateParams(startDate: Date, endDate: Date) {
    if (startDate > endDate) {
      throw new BusinessException(
        'Data inicial não pode ser maior que data final',
      );
    }

    if (startDate <= new Date()) {
      throw new BusinessException(
        'Data inicial precisa ser a partir de ' +
          DateUtil.formatToPtBr(DateUtil.plusDays(new Date(), 1)),
      );
    }
  }

  private sortInsurances(
    insurances: TravelInsurance[],
    order: TravelInsuranceOrder,
  ) {
    switch (order) {
      case TravelInsuranceOrder.NAME:
        insurances.sort((a, b) => (a.name < b.name ? -1 : 1));
        break;
      case TravelInsuranceOrder.LOWEST_PRICE:
        insurances.sort((a, b) =>
          a.rate.priceWithTax < b.rate.priceWithTax ? -1 : 1,
        );
        break;
      case TravelInsuranceOrder.BIGGEST_PRICE:
        insurances.sort((a, b) =>
          a.rate.priceWithTax > b.rate.priceWithTax ? -1 : 1,
        );
        break;
    }
  }

  private async getCountryCode(zoneId: number): Promise<string> {
    try {
      const country = await this.countryBridge.getCountry(zoneId);

      return this.translate(country.countryAbbreviation);
    } catch (error) {
      if (error.response?.status == HttpStatus.NOT_FOUND) {
        this.logger.error(error);

        throw new BusinessException(
          `zoneId informado não é válido, não foi encontrado um país para o zoneId ${zoneId}`,
        );
      } else {
        throw error;
      }
    }
  }

  private translate(country: string): string {
    const translation = this.findCountryTranslator(country);

    return translation ? translation : country;
  }

  private findCountryTranslator(country: string): string {
    return this.countryCodeTranslator.getTranslator().get(country);
  }

  private async getAvailByApplyingOpportunities(
    params: TravelInsuranceAvailQueryParamsDTO,
    requestBody: InsuranceRequestBody,
    metricToPromotion: MetricCalculate,
  ): Promise<TravelInsurance[]> {
    let insurances = await this.bridge.avail(params);

    if (insurances.length > 0) {
      insurances = await this.applyOpportunities(
        insurances,
        requestBody.selectedItems,
        metricToPromotion,
      );
    }

    return this.insurancesApplyingValueToFilter(insurances);
  }

  private insurancesApplyingValueToFilter(insurances: TravelInsurance[]) {
    return insurances.map((insurance) => {
      insurance.valueToFilter = insurance.promotion
        ? insurance.promotion.priceWithTax
        : insurance.rate.priceWithTax;
      return insurance;
    });
  }

  private buildMetaWithoutMetrics(insurances: TravelInsurance[]): MetaResponse {
    const meta = new MetaResponse();

    meta.highestValue = this.findInsuranceWithHighstValue(insurances);

    if (insurances.length > 0) {
      meta.links = this.getDefaultLinks();
      meta.groups = this.createGroups(insurances);
    } else {
      meta.links = [];
      meta.groups = [];
    }
    return meta;
  }

  private async getHasavailByApplyingOpportunities(
    rateToken: string,
    requestBody: InsuranceRequestBody,
    metric: MetricCalculate,
  ): Promise<TravelInsuranceHasavailResponse> {
    let insurance: TravelInsurance;

    try {
      insurance = await this.bridge.hasavail(rateToken);
    } catch (error) {
      if (
        error.response?.status == HttpStatus.INTERNAL_SERVER_ERROR ||
        error.response?.status == HttpStatus.NOT_FOUND
      ) {
        this.logger.error(error);

        throw new NotFoundException();
      } else {
        throw error;
      }
    }

    const metricToPromotion = MetricCalculate.newInstance();

    insurance = await this.applyOpportunity(
      insurance,
      requestBody.selectedItems,
      metricToPromotion,
    );
    insurance = this.rankingService.sortBenefits(insurance);

    return this.buildTravelInsuranceHasavailResponse(
      insurance,
      metric,
      metricToPromotion,
    );
  }

  private buildTravelInsuranceHasavailResponse(
    insurance: TravelInsurance,
    metric: MetricCalculate,
    metricToPromotion: MetricCalculate,
  ): TravelInsuranceHasavailResponse {
    const meta = new MetaForHasavailResponse();

    meta.metrics = [
      MetaUtil.applyMetrics(metric.collect(), metricToPromotion.collect()),
    ];

    return TravelInsuranceHasavailResponse.fromTravelInsuranceWithMeta(
      insurance,
      meta,
    );
  }

  private async applyOpportunities(
    insurances: TravelInsurance[],
    selectedItems: OpportunitiesItemRequest[],
    metricToPromotion: MetricCalculate,
  ) {
    return await this.opportunitiesService.applyOpportunitiesIfAnyAndReturnTravelInsurances(
      insurances,
      selectedItems,
      metricToPromotion,
    );
  }

  private async applyOpportunity(
    insurance: TravelInsurance,
    selectedItems: OpportunitiesItemRequest[],
    metricToPromotion: MetricCalculate,
  ) {
    return await this.opportunitiesService.applyOpportunitiesIfAnyAndReturnTravelInsurance(
      insurance,
      selectedItems,
      metricToPromotion,
    );
  }

  private filterInsurancesByAcceptableParams(
    insurances: TravelInsurance[],
    params: TravelInsuranceAvailQueryParamsDTO,
  ): TravelInsurance[] {
    if (!params.minValue) {
      params.minValue = 0;
    }

    if (!params.maxValue) {
      params.maxValue = this.findInsuranceWithHighstValue(insurances);
    }

    let insurancesFiltered: TravelInsurance[];

    if (params.containsName()) {
      params.name = params.name.map((name) => name.trim().toLowerCase());

      insurancesFiltered = insurances.filter(
        (insurance) =>
          params.name.includes(insurance.name.trim().toLowerCase()) &&
          insurance.valueToFilter >= params.minValue &&
          insurance.valueToFilter <= params.maxValue,
      );
    } else {
      insurancesFiltered = insurances.filter(
        (insurance) =>
          insurance.valueToFilter >= params.minValue &&
          insurance.valueToFilter <= params.maxValue,
      );
    }

    if (insurancesFiltered.length == 0) {
      return [];
    }

    return insurancesFiltered;
  }

  private buildTravelInsuranceAvailResponse(
    insurances: TravelInsurance[],
    meta: MetaResponse,
    metric: MetricCalculate,
    metricToPromotion: MetricCalculate,
  ): TravelInsuranceAvailResponse {
    const response = new TravelInsuranceAvailResponse();

    meta.metrics = [
      MetaUtil.applyMetrics(metric.collect(), metricToPromotion.collect()),
    ];

    response.content = insurances;
    response.meta = meta;

    return response;
  }

  private getDefaultLinks(): LinkResponse[] {
    const type = 'GET';

    const partner = new LinkResponse();
    partner.type = type;
    partner.rel = 'default_partner';
    partner.href = this.links.getUniversalAssistanceUrl();

    const image = new LinkResponse();
    image.type = type;
    image.rel = 'default_partner_image';
    image.href = this.links.getUniversalAssistanceLogoUrl();

    return [partner, image];
  }

  private createGroups(insurances: TravelInsurance[]): GroupResponse[] {
    let initialId = 1;
    const typesOfInsurances = this.createGroup(
      insurances,
      'Tipo de seguro viagem',
      initialId,
      false,
    );

    initialId = typesOfInsurances.items.length + 1;
    const insurancesForCovid = this.createGroup(
      insurances,
      'Seguro viagem Covid-19',
      initialId,
      true,
    );

    if (!insurancesForCovid) {
      return [typesOfInsurances];
    }

    return [typesOfInsurances, insurancesForCovid];
  }

  private createGroup(
    insurances: TravelInsurance[],
    groupName: string,
    initialId: number,
    isOnlyCovid: boolean,
  ): GroupResponse {
    let insurancesFiltered = insurances;

    if (isOnlyCovid) {
      insurancesFiltered = insurancesFiltered.filter(
        (insurance) =>
          insurance.additionalDescription &&
          insurance.additionalDescription.toLowerCase().includes('covid-19') &&
          !insurance.additionalDescription.toLowerCase().includes('não'),
      );
    }

    if (insurancesFiltered.length == 0) {
      return undefined;
    }

    const groupItems: GroupItemResponse[] = [];
    const uniqueNames = [
      ...new Set(insurancesFiltered.map((insurance) => insurance.name)),
    ];

    const all = new GroupItemResponse();
    all.id = initialId;
    all.name = `Todos (${insurancesFiltered.length})`;

    uniqueNames.forEach((name) => {
      let insurancesFilteredByName = [];

      insurancesFilteredByName = insurancesFiltered.filter(
        (insurance) => insurance.name == name,
      );

      const groupItemResponse = new GroupItemResponse();
      const insuranceName = name.trim();

      groupItemResponse.id = ++initialId;
      groupItemResponse.name = `${insuranceName} (${insurancesFilteredByName.length})`;
      groupItemResponse.value = encodeURIComponent(insuranceName);

      groupItems.push(groupItemResponse);
    });

    const group = new GroupResponse();
    group.name = groupName;
    group.items = [all, ...groupItems];

    return group;
  }

  private rankBenefits(insurances: TravelInsurance[]): TravelInsurance[] {
    return insurances.map((insurance) =>
      this.rankingService.sortBenefits(insurance),
    );
  }

  private findInsuranceWithHighstValue(insurances: TravelInsurance[]): number {
    if (insurances.length == 0) {
      return 0;
    }

    return insurances.reduce((prev, current) =>
      prev.valueToFilter > current.valueToFilter ? prev : current,
    ).valueToFilter;
  }
}
