import { HttpService } from '@nestjs/axios';
import { Injectable, Logger } from '@nestjs/common';
import { ContextService } from 'src/config/context/service/context.service';
import { EndpointConfig } from 'src/config/endpoint/endpoint.config';
import { OpportunitiesRequest } from 'src/model/opportunities/request/opportunities.request';
import { OpportunitiesResponse } from 'src/model/opportunities/response/opportunities.response';

@Injectable()
export class OpportunitiesApiBridge {
  private readonly logger: Logger;

  constructor(
    private readonly endpointConfig: EndpointConfig,
    private readonly httpService: HttpService,
    private readonly contextService: ContextService,
  ) {
    this.logger = new Logger(OpportunitiesApiBridge.name);
  }

  async hasOpportunities(
    opportunitiesRequest: OpportunitiesRequest,
  ): Promise<OpportunitiesResponse> {
    const context = this.contextService.current();
    const uri = this.endpointConfig.getOpportunitiesUrl();

    this.logger.log(`Executing http post request at ${uri}`);

    // Log the curl command for this request
    this.printCurlCommand(
      uri,
      context.getCommomRequestHeaders(),
      opportunitiesRequest.getBodyToRequest(),
    );

    const { data } =
      await this.httpService.axiosRef.post<OpportunitiesResponse>(
        uri,
        opportunitiesRequest.getBodyToRequest(),
        {
          headers: context.getCommomRequestHeaders(),
        },
      );

    return data;
  }

  // Método para montar e printar o comando curl com body para POST
  private printCurlCommand(url: string, headers: any, body: any) {
    try {
      // Iniciar o comando curl com a URL
      let curl = `curl -X POST '${url}'`;

      // Adicionar headers ao comando curl
      Object.keys(headers).forEach((key) => {
        curl += ` -H '${key}: ${headers[key]}'`;
      });

      // Adicionar body ao comando curl
      if (body) {
        curl += ` -d '${JSON.stringify(body)}'`;
      }

      // Log formatado com uma aparência mais elegante
      this.logger.log(`
================== CURL OPPORTUNITIES ==================
${curl}
=============================
`);
    } catch (error) {
      // Caso ocorra um erro, logamos o erro, mas não interrompemos a execução
      this.logger.log('Error generating CURL command', error);
    }
  }
}
