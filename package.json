{"name": "bff-typescript-express-web-travel-insurance", "version": "1.5.0", "license": "MIT", "scripts": {"prebuild": "del-cli dist", "build": "nest build", "start": "nest start", "start:local": "cross-env NODE_ENV=local nest start --watch", "start:debug": "NODE_ENV=local nest start --debug --watch", "lint": "eslint --fix .", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "clean": "del-cli node_modules && rm ./package-lock.json"}, "devDependencies": {"@nestjs/cli": "^8.0.0", "@nestjs/schematics": "^8.0.0", "@nestjs/testing": "^8.0.0", "@types/consul": "^0.40.0", "@types/express": "^4.17.13", "@types/jest": "27.5.0", "@types/multer": "^1.4.7", "@types/node": "^16.0.0", "@types/supertest": "^2.0.11", "jest": "28.0.3", "run-script-webpack-plugin": "^0.1.1", "source-map-support": "^0.5.20", "supertest": "^6.1.3", "ts-jest": "28.0.1", "ts-loader": "^9.2.3", "ts-node": "^10.0.0", "tsconfig-paths": "4.0.0", "typescript": "^4.3.5", "webpack": "^5.73.0", "webpack-node-externals": "^3.0.0"}, "dependencies": {"@nestjs/axios": "^0.0.8", "@nestjs/common": "^8.0.0", "@nestjs/config": "^2.1.0", "@nestjs/core": "^8.0.0", "@nestjs/jwt": "^10.0.1", "@nestjs/platform-express": "^8.0.0", "@nestjs/swagger": "^5.2.1", "@nestjs/terminus": "^8.1.0", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "class-transformer": "^0.5.1", "class-validator": "^0.13.2", "consul": "^0.40.0", "cross-env": "^7.0.3", "eslint": "^8.57.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^4.2.1", "reflect-metadata": "^0.1.13", "del-cli": "^5.1.0", "rxjs": "^7.2.0", "swagger-ui-express": "^4.4.0", "typescript-eslint": "^8.15.0"}, "engines": {"npm": ">=10.0.0", "node": ">=18.0.0", "yarn": "use npm!", "pnpm": "use npm!"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "roots": ["../test"], "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["converter/**/*.(t|j)s", "service/**/*.(t|j)s", "util/**/*.(t|j)s"], "moduleNameMapper": {"^src/(.*)$": "<rootDir>/$1"}, "coverageDirectory": "../coverage", "testEnvironment": "node"}}