import { ApiProperty } from '@nestjs/swagger';
import { IsDateString } from 'class-validator';
import { InsuranceStoreLinksResponse } from './insurance-store-hasavail-links.response';
import { InsuranceStoreRateResponse } from './insurance-store-hasavail-rate.response';

export class InsuranceStoreResponse {
  @ApiProperty()
  rateToken: string;

  @ApiProperty()
  eventCode: number;

  @IsDateString()
  @ApiProperty({
    type: Date,
    format: 'yyyy-MM-dd',
  })
  startDate: string;

  @IsDateString()
  @ApiProperty({
    type: Date,
    format: 'yyyy-MM-dd',
  })
  endDate: string;

  @ApiProperty()
  passengers: number;

  @ApiProperty()
  name: string;

  @ApiProperty()
  description: string;

  @ApiProperty()
  company: string;

  @ApiProperty()
  price: number;

  @ApiProperty({
    required: false,
    type: InsuranceStoreRateResponse,
    isArray: true,
  })
  rates: InsuranceStoreRateResponse[];

  @ApiProperty()
  currency: string;

  @ApiProperty()
  cancelation: boolean;

  @ApiProperty()
  links: InsuranceStoreLinksResponse;

  @ApiProperty()
  hasCombo: boolean;

  @ApiProperty()
  acceptableDocuments: string[];
}
