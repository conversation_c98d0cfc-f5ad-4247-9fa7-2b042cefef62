import { HttpService } from '@nestjs/axios';
import { Test, TestingModule } from '@nestjs/testing';
import { InsuranceApiBridge } from 'src/bridge/insurance-api.bridge';
import { TravelVoucherApiBridge } from 'src/bridge/travel-voucher-api.bridge';
import { Context } from 'src/config/context/model/context';
import { ContextService } from 'src/config/context/service/context.service';
import { EndpointConfig } from 'src/config/endpoint/endpoint.config';
import { LinksConfig } from 'src/config/logo/links.config';
import { TravelInsuranceCountryResponseDTO } from 'src/model/travel-insurance/response/travel-insurance-country.response';
import { OpportunitiesService } from 'src/service/opportunities/opportunities.service';
import { RankingService } from 'src/service/ranking/ranking.service';
import { TravelInsuranceService } from 'src/service/travel-insurance/travel-insurance.service';
import { InsuranceRequestBodyMock } from '../../mock/insurance-request-body.mock';
import { TravelInsuranceAvailQueryParamsDTOMock } from '../../mock/travel-insurance-avail-query-params-dto.mock';
import { TravelInsuranceMock } from '../../mock/travel-insurance.mock';
import { OpportunitiesMock } from '../../../test/mock/opportunities.mock';
import { ContextMock } from '../../mock/context.mock';
import { OpportunitiesApiBridge } from 'src/bridge/opportunities-api.bridge';
import { OpportunitiesResponse } from 'src/model/opportunities/response/opportunities.response';
import { OpportunitiesConfig } from 'src/config/opportunities/opportunities.config';
import { RankingConfig } from 'src/config/ranking/ranking.config';
import { DateUtil } from 'src/util/date.util';
import { BusinessException } from 'src/error/business-exception';
import { HttpException, HttpStatus, NotFoundException } from '@nestjs/common';
import { TravelInsuranceOrder } from 'src/model/travel-insurance/enums/TravelInsuranceOrder';
import { CountryCodeTranslatorConfig } from 'src/config/country-code-translator/country-code-translator.config';

describe('TravelInsuranceService', () => {
    let service: TravelInsuranceService;
    let countryBridge: TravelVoucherApiBridge;
    let bridge: InsuranceApiBridge; 
    let opportunitiesService: OpportunitiesService;
    let rankingService: RankingService;
    let links: LinksConfig;    
    
    const endpointConfigMock = {
        getCorpSvaInsuranceUrl(): string {
            return 'https://test/corp-sva-insurance';
        },
        getTravelVoucherUrl(): string {
            return 'https://test/travel-voucher';
        }
    } 

    const contextServiceMock = {
        current(): Context {
            return ContextMock.getContext();
        }
    }

    const opportunitiesApiBridgeMock = {
        hasOpportunities(): Promise<OpportunitiesResponse> {
            return Promise.resolve(OpportunitiesMock.getOpportunitiesResponse());
        } 
    }

    const opporunitiesConfigMock = {
        isItToLookForOpportunities(): boolean {
            return true;
        }
    }

    const rankingConfigMock = {
        getDefaultRanking(): number {
            return 100;
        },
        getCustomRanking(): Map<number, number> {
            const map = new Map<number, number>;
            map.set(1, 1);
            return map;
        }
    }
    const countryCodeTranslatorConfigMock = {
        getTranslator(): Map<string, string> {
            const map = new Map<string, string>;
            map.set('LX', 'LU');
            return map;
        }
    }

    const countryResponse = new TravelInsuranceCountryResponseDTO();
    countryResponse.countryAbbreviation = 'BR';
    
    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [
                TravelInsuranceService, 
                TravelVoucherApiBridge,                     
                InsuranceApiBridge,                
                {
                    provide: EndpointConfig,
                    useValue: endpointConfigMock
                },
                {
                    provide: HttpService,
                    useValue: jest.mock('axios')
                },
                {
                    provide: ContextService,
                    useValue: contextServiceMock
                },
                OpportunitiesService,
                {
                    provide: OpportunitiesApiBridge,
                    useValue: opportunitiesApiBridgeMock,
                },
                {
                    provide: OpportunitiesConfig,
                    useValue: opporunitiesConfigMock,
                },
                RankingService,                
                {
                    provide: RankingConfig,
                    useValue: rankingConfigMock,
                },                
                LinksConfig,
                {
                    provide: CountryCodeTranslatorConfig,
                    useValue: countryCodeTranslatorConfigMock,
                },
            ]                                                  
        }).compile();

        service = await module.resolve(TravelInsuranceService);             
        rankingService = await module.resolve(RankingService);        
        links = await module.resolve(LinksConfig);        
        opportunitiesService = module.get<OpportunitiesService>(OpportunitiesService);
        countryBridge = module.get<TravelVoucherApiBridge>(TravelVoucherApiBridge);
        bridge = module.get<InsuranceApiBridge>(InsuranceApiBridge);
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('should be defined', () => {           
        expect(service).toBeDefined();         
        expect(countryBridge).toBeDefined();
        expect(opportunitiesService).toBeDefined();
        expect(rankingService).toBeDefined();        
        expect(links).toBeDefined();
        expect(bridge).toBeDefined();
    });

    it('avail -> must execute avail with success', () => {
        
        const params = TravelInsuranceAvailQueryParamsDTOMock.getTravelInsuranceAvailQueryParamsDTO();
        const requestBody = InsuranceRequestBodyMock.getInsuranceRequestBody();

        const travelInsurances = TravelInsuranceMock.getTravelInsurances();
                
        const countryBridgeCalled = jest.spyOn(countryBridge, 'getCountry')
            .mockImplementation(() => Promise.resolve(countryResponse));

        const bridgeCalled = jest.spyOn(bridge, 'avail')
            .mockImplementation(() => Promise.resolve(travelInsurances));        
            
        const opportunitiesServiceCalled = jest.spyOn(opportunitiesService, 'applyOpportunitiesIfAnyAndReturnTravelInsurances')
            .mockImplementation(() => Promise.resolve(travelInsurances));

        const rankingServiceCalled = jest.spyOn(rankingService, 'sortBenefits')
            .mockImplementation(() => travelInsurances[0])
            .mockImplementation(() => travelInsurances[1]);

        service.avail(params, requestBody)
            .then((result) => {
                expect(travelInsurances).toBeTruthy();  
                expect(travelInsurances.length).toEqual(result.content.length);                
                expect(result.meta).toBeTruthy();
                expect(countryBridgeCalled).toHaveBeenCalledTimes(1);
                expect(bridgeCalled).toHaveBeenCalledTimes(1);
                expect(opportunitiesServiceCalled).toHaveBeenCalledTimes(1);
                expect(rankingServiceCalled).toHaveBeenCalledTimes(2);
            });            
    });

    it('avail -> must execute avail with success updating country by translator', () => {
        
        const params = TravelInsuranceAvailQueryParamsDTOMock.getTravelInsuranceAvailQueryParamsDTO();
        const requestBody = InsuranceRequestBodyMock.getInsuranceRequestBody();

        const travelInsurances = TravelInsuranceMock.getTravelInsurances();

        const country = new TravelInsuranceCountryResponseDTO();
        country.countryAbbreviation = 'LX';
                
        const countryBridgeCalled = jest.spyOn(countryBridge, 'getCountry')
            .mockImplementation(() => Promise.resolve(country));

        const bridgeCalled = jest.spyOn(bridge, 'avail')
            .mockImplementation(() => Promise.resolve(travelInsurances));        
            
        const opportunitiesServiceCalled = jest.spyOn(opportunitiesService, 'applyOpportunitiesIfAnyAndReturnTravelInsurances')
            .mockImplementation(() => Promise.resolve(travelInsurances));

        const rankingServiceCalled = jest.spyOn(rankingService, 'sortBenefits')
            .mockImplementation(() => travelInsurances[0])
            .mockImplementation(() => travelInsurances[1]);

        service.avail(params, requestBody)
            .then((result) => {
                expect(travelInsurances).toBeTruthy();  
                expect(travelInsurances.length).toEqual(result.content.length);                
                expect(result.meta).toBeTruthy();
                expect(countryBridgeCalled).toHaveBeenCalledTimes(1);
                expect(bridgeCalled).toHaveBeenCalledTimes(1);
                expect(opportunitiesServiceCalled).toHaveBeenCalledTimes(1);
                expect(rankingServiceCalled).toHaveBeenCalledTimes(2);
            });            
    });

    it('avail -> must execute avail with promotion with success', () => {
        
        const params = TravelInsuranceAvailQueryParamsDTOMock.getTravelInsuranceAvailQueryParamsDTO();
        const requestBody = InsuranceRequestBodyMock.getInsuranceRequestBody();

        const travelInsurances = TravelInsuranceMock.getTravelInsurancesWithPromotion();
                
        const countryBridgeCalled = jest.spyOn(countryBridge, 'getCountry')
            .mockImplementation(() => Promise.resolve(countryResponse));

        const bridgeCalled = jest.spyOn(bridge, 'avail')
            .mockImplementation(() => Promise.resolve(travelInsurances));        
            
        const opportunitiesServiceCalled = jest.spyOn(opportunitiesService, 'applyOpportunitiesIfAnyAndReturnTravelInsurances')
            .mockImplementation(() => Promise.resolve(travelInsurances));

        const rankingServiceCalled = jest.spyOn(rankingService, 'sortBenefits')
            .mockImplementation(() => travelInsurances[0])
            .mockImplementation(() => travelInsurances[1]);

        service.avail(params, requestBody)
            .then((result) => {
                expect(travelInsurances).toBeTruthy();  
                expect(travelInsurances.length).toEqual(result.content.length);  
                expect(result.content[0].promotion).toBeTruthy();             
                expect(result.content[1].promotion).toBeTruthy();
                expect(result.meta).toBeTruthy();
                expect(countryBridgeCalled).toHaveBeenCalledTimes(1);
                expect(bridgeCalled).toHaveBeenCalledTimes(1);
                expect(opportunitiesServiceCalled).toHaveBeenCalledTimes(1);
                expect(rankingServiceCalled).toHaveBeenCalledTimes(2);
            });            
    });

    it('avail -> must execute avail with success when no has insurance for covid-19', () => {
        
        const params = TravelInsuranceAvailQueryParamsDTOMock.getTravelInsuranceAvailQueryParamsDTO();
        const requestBody = InsuranceRequestBodyMock.getInsuranceRequestBody();

        const travelInsurances = TravelInsuranceMock.getTravelInsurances().map(insurance => {
            insurance.additionalDescription = '';
            return insurance;
        });
                
        const countryBridgeCalled = jest.spyOn(countryBridge, 'getCountry')
            .mockImplementation(() => Promise.resolve(countryResponse));

        const bridgeCalled = jest.spyOn(bridge, 'avail')
            .mockImplementation(() => Promise.resolve(travelInsurances));        
            
        const opportunitiesServiceCalled = jest.spyOn(opportunitiesService, 'applyOpportunitiesIfAnyAndReturnTravelInsurances')
            .mockImplementation(() => Promise.resolve(travelInsurances));

        const rankingServiceCalled = jest.spyOn(rankingService, 'sortBenefits')
            .mockImplementation(() => travelInsurances[0])
            .mockImplementation(() => travelInsurances[1]);

        service.avail(params, requestBody)
            .then((result) => {
                expect(travelInsurances).toBeTruthy();  
                expect(travelInsurances.length).toEqual(result.content.length);                
                expect(result.meta).toBeTruthy();
                expect(countryBridgeCalled).toHaveBeenCalledTimes(1);
                expect(bridgeCalled).toHaveBeenCalledTimes(1);
                expect(opportunitiesServiceCalled).toHaveBeenCalledTimes(1);
                expect(rankingServiceCalled).toHaveBeenCalledTimes(2);
            });            
    });

    it('avail -> with empty result must execute avail with success', () => {
        
        const params = TravelInsuranceAvailQueryParamsDTOMock.getTravelInsuranceAvailQueryParamsDTO();
        const requestBody = InsuranceRequestBodyMock.getInsuranceRequestBody();        
                
        const countryBridgeCalled = jest.spyOn(countryBridge, 'getCountry')
            .mockImplementation(() => Promise.resolve(countryResponse));

        const bridgeCalled = jest.spyOn(bridge, 'avail')
            .mockImplementation(() => Promise.resolve([]));       
        
        service.avail(params, requestBody)
            .then((result) => {
                expect(result.content.length).toEqual(0)                
                expect(result.meta.groups.length).toEqual(0)
                expect(result.meta.links.length).toEqual(0)
                expect(countryBridgeCalled).toHaveBeenCalledTimes(1);
                expect(bridgeCalled).toHaveBeenCalledTimes(1);                
            });            
    });

    it('avail -> must throw exception when start date is greather than end date', () => {
        
        const params = TravelInsuranceAvailQueryParamsDTOMock.getTravelInsuranceAvailQueryParamsDTO();
        const requestBody = InsuranceRequestBodyMock.getInsuranceRequestBody();

        params.startDate = DateUtil.plusDays(new Date(), 365).toISOString(); 
        
        expect(() => service.avail(params, requestBody)).rejects.toThrow(BusinessException);
    });

    it('avail -> must throw exception when start date is invalid', () => {
        
        const params = TravelInsuranceAvailQueryParamsDTOMock.getTravelInsuranceAvailQueryParamsDTO();
        const requestBody = InsuranceRequestBodyMock.getInsuranceRequestBody();

        const date = new Date();
        date.setDate(date.getDate() - 1);
        params.startDate = date.toISOString();
        
        expect(() => service.avail(params, requestBody)).rejects.toThrow(BusinessException);                                
    });

    it('avail -> must throw exception when min value param is greather than max value', () => {
        
        const params = TravelInsuranceAvailQueryParamsDTOMock.getTravelInsuranceAvailQueryParamsDTO();
        const requestBody = InsuranceRequestBodyMock.getInsuranceRequestBody();

        params.minValue = 10;
        params.maxValue = 1;
        
        expect(() => service.avail(params, requestBody)).rejects.toThrow(BusinessException);
    });

    it('avail -> must throw exception when country not found', () => {
        
        const params = TravelInsuranceAvailQueryParamsDTOMock.getTravelInsuranceAvailQueryParamsDTO();
        const requestBody = InsuranceRequestBodyMock.getInsuranceRequestBody();        

        jest.spyOn(countryBridge, 'getCountry')
            .mockImplementation(() => { throw new HttpException(
                { message: 'Error getting country', status: HttpStatus.NOT_FOUND }, HttpStatus.NOT_FOUND) });       

        expect(() => service.avail(params, requestBody)).rejects.toThrow(BusinessException);
    });

    it('avail -> must throw exception when country api return any error', () => {
        
        const params = TravelInsuranceAvailQueryParamsDTOMock.getTravelInsuranceAvailQueryParamsDTO();
        const requestBody = InsuranceRequestBodyMock.getInsuranceRequestBody();
            
        jest.spyOn(countryBridge, 'getCountry')
            .mockImplementation(() => { throw new Error('Error getting country') });

        expect(() => service.avail(params, requestBody)).rejects.toThrow(Error);                 
    });

    it('avail -> must execute avail filtering result with success', () => {
        
        const params = TravelInsuranceAvailQueryParamsDTOMock.getTravelInsuranceAvailQueryParamsDTO();        
        const requestBody = InsuranceRequestBodyMock.getInsuranceRequestBody();

        const travelInsurances = TravelInsuranceMock.getTravelInsurances();

        params.minValue = 100;
        params.maxValue = 500;
        params.name = [travelInsurances[0].name];
                
        const countryBridgeCalled = jest.spyOn(countryBridge, 'getCountry')
            .mockImplementation(() => Promise.resolve(countryResponse));

        const bridgeCalled = jest.spyOn(bridge, 'avail')
            .mockImplementation(() => Promise.resolve(travelInsurances));        
            
        const opportunitiesServiceCalled = jest.spyOn(opportunitiesService, 'applyOpportunitiesIfAnyAndReturnTravelInsurances')
            .mockImplementation(() => Promise.resolve(travelInsurances));

        const rankingServiceCalled = jest.spyOn(rankingService, 'sortBenefits')            
            .mockImplementation(() => travelInsurances[0]);

        service.avail(params, requestBody)
            .then((result) => {
                expect(travelInsurances).toBeTruthy();
                expect(result.content.length).toEqual(1);                
                expect(result.meta).toBeTruthy();
                expect(countryBridgeCalled).toHaveBeenCalledTimes(1);
                expect(bridgeCalled).toHaveBeenCalledTimes(1);
                expect(opportunitiesServiceCalled).toHaveBeenCalledTimes(1);
                expect(rankingServiceCalled).toHaveBeenCalledTimes(1);
            });            
    });

    it('avail -> must execute avail filtering result with success without min value param', () => {
        
        const params = TravelInsuranceAvailQueryParamsDTOMock.getTravelInsuranceAvailQueryParamsDTO();        
        const requestBody = InsuranceRequestBodyMock.getInsuranceRequestBody();

        const travelInsurances = TravelInsuranceMock.getTravelInsurances();
        
        params.maxValue = 500;
        params.name = [travelInsurances[0].name];
                
        const countryBridgeCalled = jest.spyOn(countryBridge, 'getCountry')
            .mockImplementation(() => Promise.resolve(countryResponse));

        const bridgeCalled = jest.spyOn(bridge, 'avail')
            .mockImplementation(() => Promise.resolve(travelInsurances));        
            
        const opportunitiesServiceCalled = jest.spyOn(opportunitiesService, 'applyOpportunitiesIfAnyAndReturnTravelInsurances')
            .mockImplementation(() => Promise.resolve(travelInsurances));

        const rankingServiceCalled = jest.spyOn(rankingService, 'sortBenefits')            
            .mockImplementation(() => travelInsurances[0]);

        service.avail(params, requestBody)
            .then((result) => {
                expect(travelInsurances).toBeTruthy();  
                expect(result.content.length).toEqual(1);                
                expect(result.meta).toBeTruthy();
                expect(countryBridgeCalled).toHaveBeenCalledTimes(1);
                expect(bridgeCalled).toHaveBeenCalledTimes(1);
                expect(opportunitiesServiceCalled).toHaveBeenCalledTimes(1);
                expect(rankingServiceCalled).toHaveBeenCalledTimes(1);
            });            
    });

    it('avail -> must execute avail filtering result with success without max value param', () => {
        
        const params = TravelInsuranceAvailQueryParamsDTOMock.getTravelInsuranceAvailQueryParamsDTO();        
        const requestBody = InsuranceRequestBodyMock.getInsuranceRequestBody();

        const travelInsurances = TravelInsuranceMock.getTravelInsurances();

        params.minValue = 10;
        params.name = [travelInsurances[0].name];
                
        const countryBridgeCalled = jest.spyOn(countryBridge, 'getCountry')
            .mockImplementation(() => Promise.resolve(countryResponse));

        const bridgeCalled = jest.spyOn(bridge, 'avail')
            .mockImplementation(() => Promise.resolve(travelInsurances));      
            
        const opportunitiesServiceCalled = jest.spyOn(opportunitiesService, 'applyOpportunitiesIfAnyAndReturnTravelInsurances')
            .mockImplementation(() => Promise.resolve(travelInsurances));

        const rankingServiceCalled = jest.spyOn(rankingService, 'sortBenefits')            
            .mockImplementation(() => travelInsurances[0]);

        service.avail(params, requestBody)
            .then((result) => {
                expect(travelInsurances).toBeTruthy();  
                expect(result.content.length).toEqual(1);                
                expect(result.meta).toBeTruthy();
                expect(countryBridgeCalled).toHaveBeenCalledTimes(1);
                expect(bridgeCalled).toHaveBeenCalledTimes(1);
                expect(opportunitiesServiceCalled).toHaveBeenCalledTimes(1);
                expect(rankingServiceCalled).toHaveBeenCalledTimes(1);
            });            
    });

     it('avail -> must execute avail filtering result with success without name param', () => {
        
        const params = TravelInsuranceAvailQueryParamsDTOMock.getTravelInsuranceAvailQueryParamsDTO();        
        const requestBody = InsuranceRequestBodyMock.getInsuranceRequestBody();

        const travelInsurances = TravelInsuranceMock.getTravelInsurances();

        params.minValue = 200;
        params.maxValue = 500;        
                
        const countryBridgeCalled = jest.spyOn(countryBridge, 'getCountry')
            .mockImplementation(() => Promise.resolve(countryResponse));

        const bridgeCalled = jest.spyOn(bridge, 'avail')
            .mockImplementation(() => Promise.resolve(travelInsurances));        
            
        const opportunitiesServiceCalled = jest.spyOn(opportunitiesService, 'applyOpportunitiesIfAnyAndReturnTravelInsurances')
            .mockImplementation(() => Promise.resolve(travelInsurances));

        const rankingServiceCalled = jest.spyOn(rankingService, 'sortBenefits')            
            .mockImplementation(() => travelInsurances[0]);

        service.avail(params, requestBody)
            .then((result) => {
                expect(travelInsurances).toBeTruthy();  
                expect(result.content.length).toEqual(1);                
                expect(result.meta).toBeTruthy();
                expect(countryBridgeCalled).toHaveBeenCalledTimes(1);
                expect(bridgeCalled).toHaveBeenCalledTimes(1);
                expect(opportunitiesServiceCalled).toHaveBeenCalledTimes(1);
                expect(rankingServiceCalled).toHaveBeenCalledTimes(1);
            });            
    });

    it('avail -> must execute avail filtering result empty with success', () => {
        
        const params = TravelInsuranceAvailQueryParamsDTOMock.getTravelInsuranceAvailQueryParamsDTO();        
        const requestBody = InsuranceRequestBodyMock.getInsuranceRequestBody();

        const travelInsurances = TravelInsuranceMock.getTravelInsurances();

        params.minValue = 200;
        params.maxValue = 500;
        params.name = ['nameMock'];
                
        const countryBridgeCalled = jest.spyOn(countryBridge, 'getCountry')
            .mockImplementation(() => Promise.resolve(countryResponse));

        const bridgeCalled = jest.spyOn(bridge, 'avail')
            .mockImplementation(() => Promise.resolve(travelInsurances));        
            
        const opportunitiesServiceCalled = jest.spyOn(opportunitiesService, 'applyOpportunitiesIfAnyAndReturnTravelInsurances')
            .mockImplementation(() => Promise.resolve(travelInsurances));        

        service.avail(params, requestBody)
            .then((result) => {
                expect(travelInsurances).toBeTruthy();
                expect(result.content.length).toEqual(0);                
                expect(countryBridgeCalled).toHaveBeenCalledTimes(1);
                expect(bridgeCalled).toHaveBeenCalledTimes(1);
                expect(opportunitiesServiceCalled).toHaveBeenCalledTimes(1);                
            });            
    });

    it('avail -> must execute avail ordering by name result with success', () => {
        
        const params = TravelInsuranceAvailQueryParamsDTOMock.getTravelInsuranceAvailQueryParamsDTO();        
        const requestBody = InsuranceRequestBodyMock.getInsuranceRequestBody();

        const travelInsurances = TravelInsuranceMock.getTravelInsurancesToOrder();
        
        params.orderBy = TravelInsuranceOrder.NAME;
                
        const countryBridgeCalled = jest.spyOn(countryBridge, 'getCountry')
            .mockImplementation(() => Promise.resolve(countryResponse));

        const bridgeCalled = jest.spyOn(bridge, 'avail')
            .mockImplementation(() => Promise.resolve(travelInsurances));        
            
        const opportunitiesServiceCalled = jest.spyOn(opportunitiesService, 'applyOpportunitiesIfAnyAndReturnTravelInsurances')
            .mockImplementation(() => Promise.resolve(travelInsurances));

        const rankingServiceCalled = jest.spyOn(rankingService, 'sortBenefits')            
            .mockImplementation(() => travelInsurances[0])
            .mockImplementation(() => travelInsurances[1])
            .mockImplementation(() => travelInsurances[2]);

        service.avail(params, requestBody)
            .then((result) => {
                expect(travelInsurances).toBeTruthy();  
                expect(result.content.length).toEqual(3);                                
                expect(result.meta).toBeTruthy();
                expect(countryBridgeCalled).toHaveBeenCalledTimes(1);
                expect(bridgeCalled).toHaveBeenCalledTimes(1);
                expect(opportunitiesServiceCalled).toHaveBeenCalledTimes(1);
                expect(rankingServiceCalled).toHaveBeenCalledTimes(3);
            });            
    });

    it('avail -> must execute avail ordering by lowest price result with success', () => {
        
        const params = TravelInsuranceAvailQueryParamsDTOMock.getTravelInsuranceAvailQueryParamsDTO();        
        const requestBody = InsuranceRequestBodyMock.getInsuranceRequestBody();

        const travelInsurances = TravelInsuranceMock.getTravelInsurancesToOrder();
        
        params.orderBy = TravelInsuranceOrder.LOWEST_PRICE;
                
        const countryBridgeCalled = jest.spyOn(countryBridge, 'getCountry')
            .mockImplementation(() => Promise.resolve(countryResponse));

        const bridgeCalled = jest.spyOn(bridge, 'avail')
            .mockImplementation(() => Promise.resolve(travelInsurances));        
            
        const opportunitiesServiceCalled = jest.spyOn(opportunitiesService, 'applyOpportunitiesIfAnyAndReturnTravelInsurances')
            .mockImplementation(() => Promise.resolve(travelInsurances));

        const rankingServiceCalled = jest.spyOn(rankingService, 'sortBenefits')            
            .mockImplementation(() => travelInsurances[0])
            .mockImplementation(() => travelInsurances[1])
            .mockImplementation(() => travelInsurances[2]);

        service.avail(params, requestBody)
            .then((result) => {
                expect(travelInsurances).toBeTruthy();  
                expect(result.content.length).toEqual(3);                                
                expect(result.meta).toBeTruthy();
                expect(countryBridgeCalled).toHaveBeenCalledTimes(1);
                expect(bridgeCalled).toHaveBeenCalledTimes(1);
                expect(opportunitiesServiceCalled).toHaveBeenCalledTimes(1);
                expect(rankingServiceCalled).toHaveBeenCalledTimes(3);
            });            
    });

    it('avail -> must execute avail ordering by biggest price result with success', () => {
        
        const params = TravelInsuranceAvailQueryParamsDTOMock.getTravelInsuranceAvailQueryParamsDTO();        
        const requestBody = InsuranceRequestBodyMock.getInsuranceRequestBody();

        const travelInsurances = TravelInsuranceMock.getTravelInsurancesToOrder();
        
        params.orderBy = TravelInsuranceOrder.BIGGEST_PRICE;
                
        const countryBridgeCalled = jest.spyOn(countryBridge, 'getCountry')
            .mockImplementation(() => Promise.resolve(countryResponse));

        const bridgeCalled = jest.spyOn(bridge, 'avail')
            .mockImplementation(() => Promise.resolve(travelInsurances));        
            
        const opportunitiesServiceCalled = jest.spyOn(opportunitiesService, 'applyOpportunitiesIfAnyAndReturnTravelInsurances')
            .mockImplementation(() => Promise.resolve(travelInsurances));

        const rankingServiceCalled = jest.spyOn(rankingService, 'sortBenefits')            
            .mockImplementation(() => travelInsurances[0])
            .mockImplementation(() => travelInsurances[1])
            .mockImplementation(() => travelInsurances[2]);

        service.avail(params, requestBody)
            .then((result) => {
                expect(travelInsurances).toBeTruthy();  
                expect(result.content.length).toEqual(3);                                
                expect(result.meta).toBeTruthy();
                expect(countryBridgeCalled).toHaveBeenCalledTimes(1);
                expect(bridgeCalled).toHaveBeenCalledTimes(1);
                expect(opportunitiesServiceCalled).toHaveBeenCalledTimes(1);
                expect(rankingServiceCalled).toHaveBeenCalledTimes(3);
            });            
    });

    it('hasavail -> must execute hasavail with success', () => {
        
        const requestBody = InsuranceRequestBodyMock.getInsuranceRequestBody();
        const travelInsurance = TravelInsuranceMock.getTravelInsurances()[0];
        const rateToken = travelInsurance.insuranceToken;
        
        const bridgeCalled = jest.spyOn(bridge, 'hasavail')
            .mockImplementation(() => Promise.resolve(travelInsurance));        
            
        const opportunitiesServiceCalled = jest.spyOn(opportunitiesService, 'applyOpportunitiesIfAnyAndReturnTravelInsurance')
            .mockImplementation(() => Promise.resolve(travelInsurance));

        const rankingServiceCalled = jest.spyOn(rankingService, 'sortBenefits')
            .mockImplementation(() => travelInsurance);

        service.hasavail(rateToken, requestBody)
            .then((result) => {
                expect(travelInsurance).toBeTruthy();
                expect(result.meta).toBeTruthy();                
                expect(bridgeCalled).toHaveBeenCalledTimes(1);
                expect(opportunitiesServiceCalled).toHaveBeenCalledTimes(1);
                expect(rankingServiceCalled).toHaveBeenCalledTimes(1);
            });            
    }); 

    it('hasavail -> must execute hasavail when insurance not found', () => {
        
        const requestBody = InsuranceRequestBodyMock.getInsuranceRequestBody();
        const travelInsurance = TravelInsuranceMock.getTravelInsurances()[0];
        const rateToken = travelInsurance.insuranceToken;
        
        jest.spyOn(bridge, 'hasavail')
            .mockImplementation(() => { throw new HttpException(
                { message: 'Error getting insurance', status: HttpStatus.NOT_FOUND }, HttpStatus.NOT_FOUND) });
        
        expect(() => service.hasavail(rateToken, requestBody)).rejects.toThrow(NotFoundException);           
    });
    
    it('hasavail -> must execute hasavail with error', () => {
        
        const requestBody = InsuranceRequestBodyMock.getInsuranceRequestBody();
        const travelInsurance = TravelInsuranceMock.getTravelInsurances()[0];
        const rateToken = travelInsurance.insuranceToken;
        
        jest.spyOn(bridge, 'hasavail')
            .mockImplementation(() => { throw new HttpException(
                { message: 'Error getting insurance', status: HttpStatus.INTERNAL_SERVER_ERROR }, HttpStatus.INTERNAL_SERVER_ERROR) });
        
        expect(() => service.hasavail(rateToken, requestBody)).rejects.toThrow(NotFoundException);           
    }); 

    it('hasavail -> must execute hasavail with any error', () => {
        
        const requestBody = InsuranceRequestBodyMock.getInsuranceRequestBody();
        const travelInsurance = TravelInsuranceMock.getTravelInsurances()[0];
        const rateToken = travelInsurance.insuranceToken;
        
        jest.spyOn(bridge, 'hasavail')
            .mockImplementation(() => { throw new Error('Error getting insurance') });
        
        expect(() => service.hasavail(rateToken, requestBody)).rejects.toThrow(Error);           
    });
 
});