services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - VERACODE_APP_ID=9d3233cb10a90d3cc796f68bd7939d11
        - VERACODE_API_KEY=11b31b139d9be177d461ab99d809e643471ebdca0c37959d96a064dc58c0f4d18a9235fc8dac042992f24396d86658e67f1d9793e3416ca8d930958a90b785a8
        - BUILD_ID=vTeste
    ports:
      - '8080:8080'
    environment:
      - PREFIX=bff-typescript-express-web-travel-insurance
      - VAULT_HOST=vault.qa.cvc.intra
      - VAULT_SCHEME=http
      - CONSUL_HOST=consul.qa.cvc.intra
      - CONSUL_PORT="8500"
      - CONSUL_PATH=corp-sva/bff-typescript-express-web-travel-insurance
      - NODE_ENV=qa
